'use client';

import { useCallback, useEffect, useState } from 'react';
import './styles.css';


import { FileTextIcon, VideoIcon } from 'lucide-react';
import { Settings, Users } from 'lucide-react';
import { useTranslation } from 'react-i18next';

import { Button } from '@kit/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@kit/ui/card';
import { If } from '@kit/ui/if';
import { toast } from '@kit/ui/sonner';
import { Spinner } from '@kit/ui/spinner';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@kit/ui/tabs';
import { Trans } from '@kit/ui/trans';

import {
  getProjectDetailsAction,
  pauseProcessingAction,
  startProcessingAction,
  generateAllSegmentPromptsAction,
  generateAllSegmentImagesAction,
  generateSingleSegmentPromptAction,
  generateSingleSegmentImageAction,
  extractSegmentCharactersAction,
  extractCharactersAction,
  analyzeAllSegmentCharactersAction,
  reanalyzeAllSegmentCharactersAction,
  regenerateAllPromptsAction,
  regenerateAllImagesAction,
  generateAllSegmentAudioAction,
  regenerateAllAudioAction,
  mergeVideoAction,
  regenerateVideoAction,
  checkVideoReadinessAction,
  deleteVideoAction,
} from '../../../_lib/server/server-actions';
import { DeleteSegmentDialog } from './delete-segment-dialog';
import { EditSegmentDialog } from './edit-segment-dialog';
import { ProjectHeader } from './project-header';
import { ProjectProgress } from './project-progress';
import { ProjectSettings } from './project-settings';
import { ResegmentTextDialog } from './resegment-text-dialog';
import { OptimizedSegmentsView } from './optimized-segments-view';
import { SegmentsPagination } from './segments-pagination';
import { SegmentsToolbar } from './segments-toolbar';
import { VideoPreview } from './video-preview';
import { CharactersPanel } from './characters-panel';
import { ConfirmDialog } from './confirm-dialog';
import { useSupabase } from '@kit/supabase/hooks/use-supabase';
import { useUserWorkspace } from '@kit/accounts/hooks/use-user-workspace';
import { ProjectSettingsFormData, getDefaultSettings } from '../../../_lib/schema/project-settings.schema';

type ProjectWithDetails = {
  project: {
    id: string;
    title: string;
    description: string | null;
    original_text: string;
    status: string;
    progress: number;
    created_at: string;
    [key: string]: any;
  };
  segments: Array<{
    id: string;
    segment_index: number;
    text_content: string;
    status: string;
    image_prompt?: string | null;
    prompt_generated_at?: string | null;
    prompt_generation_task_id?: string | null;
    audioFile?: any;
    imageFile?: any;
    characters?: Array<{
      id: string;
      name: string;
      image_prompt?: string | null;
    }>;
    [key: string]: any;
  }>;
  characters?: Array<{
    id: string;
    name: string;
    aliases: string[] | null;
    image_prompt: string | null;
    created_at: string;
    updated_at: string;
    [key: string]: any;
  }>;
  video?: any;
};

interface ProjectDetailProps {
  projectId: string;
}

export function ProjectDetail({ projectId }: ProjectDetailProps) {
  const { t } = useTranslation();
  const client = useSupabase();
  const { user } = useUserWorkspace();
  const [projectData, setProjectData] = useState<ProjectWithDetails | null>(
    null,
  );
  const [loading, setLoading] = useState(true);
  const [isProcessing, setIsProcessing] = useState(false);
  const [isGeneratingPrompts, setIsGeneratingPrompts] = useState(false);
  const [isGeneratingImages, setIsGeneratingImages] = useState(false);
  const [isGeneratingAudio, setIsGeneratingAudio] = useState(false);
  const [isMergingVideo, setIsMergingVideo] = useState(false);
  const [isExtractingCharacters, setIsExtractingCharacters] = useState(false);
  const [isAnalyzingSegments, setIsAnalyzingSegments] = useState(false);

  // 状态管理
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [currentPage, setCurrentPage] = useState(1);
  const segmentsPerPage = 20;

  // Dialog states
  const [editSegmentDialog, setEditSegmentDialog] = useState<{
    open: boolean;
    segment: any;
  }>({ open: false, segment: null });

  const [deleteSegmentDialog, setDeleteSegmentDialog] = useState<{
    open: boolean;
    segment: any;
  }>({ open: false, segment: null });

  const [resegmentDialog, setResegmentDialog] = useState(false);
  const [settingsDialog, setSettingsDialog] = useState(false);

  // 确认对话框状态
  const [confirmDialog, setConfirmDialog] = useState<{
    open: boolean;
    title: string;
    description: string;
    onConfirm: () => void;
    variant?: 'default' | 'destructive';
  }>({
    open: false,
    title: '',
    description: '',
    onConfirm: () => { },
  });

  // 项目设置 - 默认设置或从项目数据中解析
  const [projectSettings, setProjectSettings] = useState<ProjectSettingsFormData>(getDefaultSettings());

  // 显示确认对话框的辅助函数
  const showConfirmDialog = (title: string, description: string, onConfirm: () => void, variant: 'default' | 'destructive' = 'default') => {
    setConfirmDialog({
      open: true,
      title,
      description,
      onConfirm,
      variant,
    });
  };

  const loadProjectData = useCallback(async () => {
    try {
      setLoading(true);
      const result = await getProjectDetailsAction({ projectId });

      if (result.success && result.data) {
        setProjectData(result.data);
        setIsProcessing(result.data.project.status === 'processing');

        // 解析项目设置
        if (result.data.project.settings) {
          try {
            const parsedSettings = typeof result.data.project.settings === 'string'
              ? JSON.parse(result.data.project.settings)
              : result.data.project.settings;
            setProjectSettings({ ...getDefaultSettings(), ...parsedSettings });
          } catch (error) {
            console.warn('Failed to parse project settings, using defaults:', error);
            setProjectSettings(getDefaultSettings());
          }
        } else {
          setProjectSettings(getDefaultSettings());
        }
      } else {
        toast.error(<Trans i18nKey="novel2video:project.loadError" />);
      }
    } catch (error) {
      console.error('Failed to load project:', error);
      toast.error(<Trans i18nKey="common:genericError" />);
    } finally {
      setLoading(false);
    }
  }, [projectId]);

  // 静默刷新项目数据（不显示loading状态）
  const refreshProjectData = useCallback(async () => {
    try {
      const result = await getProjectDetailsAction({ projectId });

      if (result.success && result.data) {
        setProjectData(result.data);
        setIsProcessing(result.data.project.status === 'processing');
      }
    } catch (error) {
      console.error('Failed to refresh project data:', error);
      // 静默失败，不显示错误提示
    }
  }, [projectId]);

  useEffect(() => {
    loadProjectData();
  }, [loadProjectData]);

  const handleStartProcessing = async () => {
    try {
      setIsProcessing(true);
      const result = await startProcessingAction({ projectId });
      if (result.success && projectData) {
        toast.success(
          <Trans i18nKey="novel2video:actions.startProcessing.success" />,
        );
        setProjectData({
          ...projectData,
          project: {
            ...projectData.project,
            status: 'processing',
            progress: 0,
          },
        });
      } else {
        toast.error(
          result.message || (
            <Trans i18nKey="novel2video:actions.startProcessing.error" />
          ),
        );
        setIsProcessing(false);
      }
    } catch (error) {
      console.error('Error starting processing:', error);
      toast.error(<Trans i18nKey="common:genericError" />);
      setIsProcessing(false);
    }
  };

  const handlePauseProcessing = async () => {
    try {
      const result = await pauseProcessingAction({ projectId });
      if (result.success && projectData) {
        toast.success(
          <Trans i18nKey="novel2video:actions.pauseProcessing.success" />,
        );
        setIsProcessing(false);
        setProjectData({
          ...projectData,
          project: {
            ...projectData.project,
            status: 'draft',
          },
        });
      } else {
        toast.error(
          result.message || (
            <Trans i18nKey="novel2video:actions.pauseProcessing.error" />
          ),
        );
      }
    } catch (error) {
      console.error('Error pausing processing:', error);
      toast.error(<Trans i18nKey="common:genericError" />);
    }
  };

  const handleRefresh = () => {
    loadProjectData();
  };

  const handleGeneratePrompts = async () => {
    if (!projectData) return;

    const promptMissingCount = projectData.segments.filter(s => !s.image_prompt).length;
    if (promptMissingCount === 0) {
      toast.error(<Trans i18nKey="novel2video:project.errors.noSegmentsToProcess" />);
      return;
    }

    try {
      setIsGeneratingPrompts(true);
      const result = await generateAllSegmentPromptsAction({
        projectId,
        style: t('novel2video:styles.realistic'),
        context: t('novel2video:contexts.novelScene'),
        priority: 'normal',
      });

      if (result.success) {
        toast.success(result.message);
      } else {
        toast.error(<Trans i18nKey="novel2video:project.errors.generatePromptsFailed" />);
      }
    } catch (error) {
      console.error('Error generating prompts:', error);
      toast.error(<Trans i18nKey="novel2video:errors.generatePromptsFailed" />);
    } finally {
      setIsGeneratingPrompts(false);
    }
  };

  const handleGenerateImages = async () => {
    if (!projectData) return;

    try {
      setIsGeneratingImages(true);

      const result = await generateAllSegmentImagesAction({
        projectId,
        priority: 'normal',
      });

      if (result.success) {
        toast.success(result.message);
      } else {
        toast.error(<Trans i18nKey="novel2video:project.errors.generateImagesFailed" />);
      }
    } catch (error) {
      console.error('Error generating images:', error);
      toast.error(<Trans i18nKey="novel2video:errors.generateImagesFailed" />);
    } finally {
      setIsGeneratingImages(false);
    }
  };

  const handleGenerateAudio = async () => {
    if (!projectData) return;

    const segmentsWithoutAudio = projectData.segments.filter(s => !s.audioFile);
    if (segmentsWithoutAudio.length === 0) {
      toast.error(<Trans i18nKey="novel2video:errors.allAudioGenerated" />);
      return;
    }

    try {
      setIsGeneratingAudio(true);

      const result = await generateAllSegmentAudioAction({
        projectId,
        priority: 'normal',
      });

      if (result.success) {
        toast.success(result.message);
      } else {
        toast.error(<Trans i18nKey="novel2video:errors.generateAudioFailed" />);
      }
    } catch (error) {
      console.error('Error generating audio:', error);
      toast.error(<Trans i18nKey="novel2video:errors.generateAudioFailed" />);
    } finally {
      setIsGeneratingAudio(false);
    }
  };

  const handleRegenerateAllAudio = async () => {
    if (!projectData) return;

    const segmentsWithAudio = projectData.segments.filter(s => s.audioFile).length;
    if (segmentsWithAudio > 0) {
      showConfirmDialog(
        t('novel2video:confirmDialogs.regenerateAllAudio.title'),
        t('novel2video:confirmDialogs.regenerateAllAudio.message', { count: segmentsWithAudio }),
        async () => {
          await performRegenerateAllAudio();
        },
        'destructive'
      );
      return;
    }

    await performRegenerateAllAudio();
  };

  const performRegenerateAllAudio = async () => {
    try {
      setIsGeneratingAudio(true);

      const result = await regenerateAllAudioAction({
        projectId,
        priority: 'normal',
      });

      if (result.success) {
        toast.success(result.message);
      } else {
        toast.error(<Trans i18nKey="novel2video:errors.regenerateAudioFailed" />);
      }
    } catch (error) {
      console.error('Error regenerating audio:', error);
      toast.error(<Trans i18nKey="novel2video:errors.regenerateAudioFailed" />);
    } finally {
      setIsGeneratingAudio(false);
    }
  };

  const handleExtractCharacters = async () => {
    if (!projectData) return;

    // 如果已经有人物数据，询问是否重新提取
    if (projectData.characters && projectData.characters.length > 0) {
      showConfirmDialog(
        t('novel2video:confirmDialogs.reextractCharacters.title'),
        t('novel2video:confirmDialogs.reextractCharacters.message', { count: projectData.characters.length }),
        async () => {
          await performExtractCharacters();
        },
        'destructive'
      );
      return;
    }

    await performExtractCharacters();
  };

  const performExtractCharacters = async () => {
    try {
      setIsExtractingCharacters(true);
      const result = await extractCharactersAction({ projectId });

      if (result.success) {
        toast.success(result.message || t('novel2video:successMessages.characterExtracted'));
      } else {
        toast.error(result.error || t('novel2video:errors.extractCharactersFailed'));
      }
    } catch (error) {
      console.error('Error extracting characters:', error);
      toast.error(<Trans i18nKey="novel2video:errors.extractCharactersFailed" />);
    } finally {
      setIsExtractingCharacters(false);
    }
  };

  const handleAnalyzeAllSegmentCharacters = async () => {
    if (!projectData) return;

    // 检查是否有人物数据
    if (!projectData.characters || projectData.characters.length === 0) {
      toast.error(<Trans i18nKey="novel2video:errors.needExtractCharactersFirst" />);
      return;
    }

    try {
      setIsAnalyzingSegments(true);
      const result = await analyzeAllSegmentCharactersAction({ projectId });

      if (result.success) {
        toast.success(result.message || t('novel2video:successMessages.charactersAnalyzed'));
      } else {
        toast.error(<Trans i18nKey="novel2video:errors.analyzeCharactersFailed" />);
      }
    } catch (error) {
      console.error('Error analyzing segment characters:', error);
      toast.error(<Trans i18nKey="novel2video:errors.analyzeCharactersFailed" />);
    } finally {
      setIsAnalyzingSegments(false);
    }
  };

  const handleReanalyzeAllSegmentCharacters = async () => {
    if (!projectData) return;

    // 检查是否有人物数据
    if (!projectData.characters || projectData.characters.length === 0) {
      toast.error(<Trans i18nKey="novel2video:errors.needExtractCharactersFirst" />);
      return;
    }

    // 确认是否重新分析所有段落
    const segmentsWithCharacters = projectData.segments.filter(s => s.characters && s.characters.length > 0).length;
    if (segmentsWithCharacters > 0) {
      showConfirmDialog(
        t('novel2video:confirmDialogs.reanalyzeAllCharacters.title'),
        t('novel2video:confirmDialogs.reanalyzeAllCharacters.message', { count: segmentsWithCharacters }),
        async () => {
          await performReanalyzeAllSegmentCharacters();
        },
        'destructive'
      );
      return;
    }

    await performReanalyzeAllSegmentCharacters();
  };

  const performReanalyzeAllSegmentCharacters = async () => {
    try {
      setIsAnalyzingSegments(true);
      const result = await reanalyzeAllSegmentCharactersAction({ projectId });

      if (result.success) {
        toast.success(result.message || t('novel2video:successMessages.charactersReanalyzed'));
      } else {
        toast.error(<Trans i18nKey="novel2video:errors.reanalyzeCharactersFailed" />);
      }
    } catch (error) {
      console.error('Error re-analyzing segment characters:', error);
      toast.error(<Trans i18nKey="novel2video:errors.reanalyzeCharactersFailed" />);
    } finally {
      setIsAnalyzingSegments(false);
    }
  };

  const handleRegenerateAllPrompts = async () => {
    if (!projectData) return;

    const promptGeneratedSegments = projectData.segments.filter(s => s.image_prompt).length;
    if (promptGeneratedSegments > 0) {
      showConfirmDialog(
        t('novel2video:confirmDialogs.regenerateAllPrompts.title'),
        t('novel2video:confirmDialogs.regenerateAllPrompts.message', { count: promptGeneratedSegments }),
        async () => {
          await performRegenerateAllPrompts();
        },
        'destructive'
      );
      return;
    }

    await performRegenerateAllPrompts();
  };

  const performRegenerateAllPrompts = async () => {
    try {
      setIsGeneratingPrompts(true);
      const result = await regenerateAllPromptsAction({
        projectId,
        style: t('novel2video:styles.realistic'),
        context: t('novel2video:contexts.novelScene'),
        priority: 'normal',
      });

      if (result.success) {
        toast.success(result.message || t('novel2video:successMessages.promptsRegenerated'));
      } else {
        toast.error(<Trans i18nKey="novel2video:errors.regeneratePromptsFailed" />);
      }
    } catch (error) {
      console.error('Error regenerating all prompts:', error);
      toast.error(<Trans i18nKey="novel2video:errors.regeneratePromptsFailed" />);
    } finally {
      setIsGeneratingPrompts(false);
    }
  };

  const handleRegenerateAllImages = async (configId?: string) => {
    if (!projectData) return;

    const imageGeneratedSegments = projectData.segments.filter(s => s.imageFile).length;
    if (imageGeneratedSegments > 0) {
      showConfirmDialog(
        t('novel2video:confirmDialogs.regenerateAllImages.title'),
        t('novel2video:confirmDialogs.regenerateAllImages.message', { count: imageGeneratedSegments }),
        async () => {
          await performRegenerateAllImages(configId);
        },
        'destructive'
      );
      return;
    }

    await performRegenerateAllImages(configId);
  };

  const performRegenerateAllImages = async (configId?: string) => {
    try {
      setIsGeneratingImages(true);

      const result = await regenerateAllImagesAction({
        projectId,
        priority: 'normal',
      });

      if (result.success) {
        toast.success(result.message || t('novel2video:successMessages.imagesRegenerated'));
      } else {
        toast.error(<Trans i18nKey="novel2video:errors.regenerateImagesFailed" />);
      }
    } catch (error) {
      console.error('Error regenerating all images:', error);
      toast.error(<Trans i18nKey="novel2video:errors.regenerateImagesFailed" />);
    } finally {
      setIsGeneratingImages(false);
    }
  };

  const handleMergeVideo = async () => {
    if (!projectData) return;

    try {
      setIsMergingVideo(true);

      // 首先检查准备状态
      const readinessResult = await checkVideoReadinessAction({
        projectId,
      });

      if (!readinessResult.success) {
        toast.error(<Trans i18nKey="novel2video:feedback.checkVideoReadinessFailed" />);
        return;
      }

      const { data: readiness } = readinessResult;

      if (!readiness.isReady) {
        toast.error(<Trans i18nKey="novel2video:errors.missingMediaFiles" values={{ count: readiness.missingMedia }} />);
        return;
      }

      if (readiness.hasVideo) {
        showConfirmDialog(
          t('novel2video:confirmDialogs.recomposeVideo.title'),
          t('novel2video:confirmDialogs.recomposeVideo.message'),
          async () => {
            await performRegenerateVideo();
          },
          'destructive'
        );
        return;
      }

      await performMergeVideo();
    } catch (error) {
      console.error('Error checking video readiness:', error);
      toast.error(<Trans i18nKey="novel2video:feedback.checkVideoReadinessFailed" />);
    } finally {
      setIsMergingVideo(false);
    }
  };

  const performMergeVideo = async () => {
    try {
      setIsMergingVideo(true);

      const result = await mergeVideoAction({
        projectId,
        priority: 'normal',
      });

      if (result.success) {
        toast.success(result.message);
        handleRefresh(); // 刷新数据以显示新生成的视频
      } else {
        toast.error(<Trans i18nKey="novel2video:errors.videoComposeFailed" />);
      }
    } catch (error) {
      console.error('Error merging video:', error);
      toast.error(<Trans i18nKey="novel2video:errors.videoComposeFailed" />);
    } finally {
      setIsMergingVideo(false);
    }
  };

  const performRegenerateVideo = async () => {
    try {
      setIsMergingVideo(true);

      const result = await regenerateVideoAction({
        projectId,
        priority: 'normal',
      });

      if (result.success) {
        toast.success(result.message);
        handleRefresh(); // 刷新数据以显示重新生成的视频
      } else {
        toast.error(<Trans i18nKey="novel2video:feedback.videoRegeneratedFailed" />);
      }
    } catch (error) {
      console.error('Error regenerating video:', error);
      toast.error(<Trans i18nKey="novel2video:feedback.videoRegeneratedFailed" />);
    } finally {
      setIsMergingVideo(false);
    }
  };

  const handleRegenerateVideo = async () => {
    if (!projectData) return;

    showConfirmDialog(
      t('novel2video:confirmDialogs.regenerateVideo.title'),
      t('novel2video:confirmDialogs.regenerateVideo.message'),
      async () => {
        try {
          setIsMergingVideo(true);

          const result = await regenerateVideoAction({
            projectId,
            priority: 'high',
          });

          if (result.success) {
            toast.success(result.message);
          } else {
            toast.error(<Trans i18nKey="novel2video:feedback.videoRegeneratedFailed" />);
          }
        } catch (error) {
          console.error('Error regenerating video:', error);
          toast.error(<Trans i18nKey="novel2video:feedback.videoRegeneratedFailed" />);
        } finally {
          setIsMergingVideo(false);
        }
      },
      'destructive'
    );
  };

  const handleDeleteVideo = async () => {
    if (!projectData?.video) return;

    showConfirmDialog(
      t('novel2video:confirmDialogs.deleteVideo.title'),
      t('novel2video:confirmDialogs.deleteVideo.message'),
      async () => {
        try {
          const result = await deleteVideoAction({
            projectId,
          });

          if (result.success) {
            toast.success(t('novel2video:successMessages.videoDeleted'));
            handleRefresh();
          } else {
            toast.error(<Trans i18nKey="novel2video:errors.videoDeleteFailed" />);
          }
        } catch (error) {
          console.error('Error deleting video:', error);
          toast.error(<Trans i18nKey="novel2video:feedback.deleteVideoFailed" />);
        }
      },
      'destructive'
    );
  };

  useEffect(() => {
    if (!projectId || !client || !user?.id) {
      console.log('🔴 Direct realtime disabled:', { projectId, client: !!client, userId: user?.id });
      return;
    }

    console.log('🟢 Setting up direct realtime for project:', projectId);
    const channel = client.channel(`direct-db-${projectId}`);

    // 1. 监听segments表直接更新
    channel.on(
      'postgres_changes',
      {
        event: 'UPDATE',
        schema: 'public',
        table: 'novel2video_segments',
        filter: `project_id=eq.${projectId}`,
      },
      (payload) => {
        const updatedSegment = payload.new;
        console.log('📝 Direct segment update from DB:', updatedSegment);

        // 直接更新projectData中的segment
        setProjectData((prevData) => {
          if (!prevData) return prevData;
          return {
            ...prevData,
            segments: prevData.segments.map((segment) =>
              segment.id === updatedSegment.id ? { ...segment, ...updatedSegment } : segment
            ),
          };
        });
      }
    );

    // 2. 监听图片文件生成
    channel.on(
      'postgres_changes',
      {
        event: '*',
        schema: 'public',
        table: 'novel2video_image_files',
      },
      async (payload) => {
        const imageFile = payload.new as any;
        if (!imageFile?.segment_id) return;

        // 检查是否属于当前项目
        const { data: segment } = await client
          .from('novel2video_segments')
          .select('project_id')
          .eq('id', imageFile.segment_id)
          .single();

        if (segment?.project_id !== projectId) return;

        console.log('🖼️ Direct image update from DB:', imageFile);

        // 直接更新projectData中的imageFile
        setProjectData((prevData) => {
          if (!prevData) return prevData;
          return {
            ...prevData,
            segments: prevData.segments.map((segment) =>
              segment.id === imageFile.segment_id
                ? { ...segment, imageFile }
                : segment
            ),
          };
        });

        if (payload.eventType === 'INSERT') {
          toast.success(t('novel2video:successMessages.newImageGenerated'));
        }
      }
    );

    // 3. 监听音频文件生成
    channel.on(
      'postgres_changes',
      {
        event: '*',
        schema: 'public',
        table: 'novel2video_audio_files',
      },
      async (payload) => {
        const audioFile = payload.new as any;
        if (!audioFile?.segment_id) return;

        // 检查是否属于当前项目
        const { data: segment } = await client
          .from('novel2video_segments')
          .select('project_id')
          .eq('id', audioFile.segment_id)
          .single();

        if (segment?.project_id !== projectId) return;

        console.log('🔊 Direct audio update from DB:', audioFile);

        // 直接更新projectData中的audioFile
        setProjectData((prevData) => {
          if (!prevData) return prevData;
          return {
            ...prevData,
            segments: prevData.segments.map((segment) =>
              segment.id === audioFile.segment_id
                ? { ...segment, audioFile }
                : segment
            ),
          };
        });

        if (payload.eventType === 'INSERT') {
          toast.success(t('novel2video:successMessages.audioGenerated'));
        }
      }
    );

    // 4. 监听段落人物关联变化
    channel.on(
      'postgres_changes',
      {
        event: '*',
        schema: 'public',
        table: 'novel2video_segment_characters',
      },
      async (payload) => {
        console.log('🎭 Segment character association update:', payload);

        // 精细化更新：只更新受影响的段落的人物关联信息
        const segmentCharacterData = payload.new as any;

        if (payload.eventType === 'INSERT' && segmentCharacterData?.segment_id) {
          // 获取更新后的段落人物信息
          const { data: updatedSegmentCharacters } = await client
            .from('novel2video_segments')
            .select(`
              id,
              characters:novel2video_segment_characters(
                character:novel2video_characters(id, name, aliases, image_prompt)
              )
            `)
            .eq('id', segmentCharacterData.segment_id)
            .single();

          if (updatedSegmentCharacters) {
            // 只更新特定段落的人物信息
            setProjectData((prevData) => {
              if (!prevData) return prevData;
              return {
                ...prevData,
                segments: prevData.segments.map((segment) =>
                  segment.id === segmentCharacterData.segment_id
                    ? {
                      ...segment,
                      characters: updatedSegmentCharacters.characters?.map((c: any) => c.character) || []
                    }
                    : segment
                ),
              };
            });
          }
        } else if (payload.eventType === 'DELETE') {
          // 删除事件：移除段落的人物关联
          const deletedData = payload.old as any;
          if (deletedData?.segment_id) {
            setProjectData((prevData) => {
              if (!prevData) return prevData;
              return {
                ...prevData,
                segments: prevData.segments.map((segment) =>
                  segment.id === deletedData.segment_id
                    ? { ...segment, characters: [] }
                    : segment
                ),
              };
            });
          }
        }
      }
    );

    // 5. 监听项目人物变化
    channel.on(
      'postgres_changes',
      {
        event: '*',
        schema: 'public',
        table: 'novel2video_characters',
        filter: `project_id=eq.${projectId}`,
      },
      async (payload) => {
        console.log('👤 Project characters update:', payload);

        // 只更新人物列表，不进行全量刷新
        try {
          const { data: updatedCharacters } = await client
            .from('novel2video_characters')
            .select('*')
            .eq('project_id', projectId)
            .order('created_at', { ascending: true });

          if (updatedCharacters) {
            setProjectData((prevData) => {
              if (!prevData) return prevData;
              return {
                ...prevData,
                characters: updatedCharacters,
              };
            });

            if (payload.eventType === 'INSERT') {
              toast.success(t('novel2video:successMessages.newCharacterExtracted'));
            }
          }
        } catch (error) {
          console.error('Failed to update characters:', error);
        }
      }
    );

    const subscription = channel.subscribe((status) => {
      console.log('📡 Direct realtime subscription status:', status);
    });

    return () => {
      subscription.unsubscribe();
    };
  }, [projectId, client, user?.id]);

  // 乐观更新方法 - 编辑段落
  const handleSegmentUpdated = useCallback(
    (segmentId: string, newTextContent: string) => {
      if (!projectData) return;

      setProjectData((prevData) => {
        if (!prevData) return prevData;

        return {
          ...prevData,
          segments: prevData.segments.map((segment) =>
            segment.id === segmentId
              ? { ...segment, text_content: newTextContent }
              : segment,
          ),
        };
      });
    },
    [projectData],
  );

  // 乐观更新方法 - 更新单个segment状态
  const handleSegmentStatusUpdated = useCallback(
    (segmentId: string, updates: Partial<any>) => {
      if (!projectData) return;

      setProjectData((prevData) => {
        if (!prevData) return prevData;

        return {
          ...prevData,
          segments: prevData.segments.map((segment) =>
            segment.id === segmentId
              ? { ...segment, ...updates }
              : segment,
          ),
        };
      });
    },
    [projectData],
  );

  // 乐观更新方法 - 删除段落
  const handleSegmentDeleted = useCallback(
    (segmentId: string) => {
      if (!projectData) return;

      setProjectData((prevData) => {
        if (!prevData) return prevData;

        return {
          ...prevData,
          segments: prevData.segments.filter(
            (segment) => segment.id !== segmentId,
          ),
        };
      });
    },
    [projectData],
  );

  const handleEditSegment = (segment: any) => {
    setEditSegmentDialog({ open: true, segment });
  };

  const handleDeleteSegment = (segment: any) => {
    setDeleteSegmentDialog({ open: true, segment });
  };

  // 单个段落操作处理函数
  const handleAnalyzeSingleSegmentCharacters = async (segment: any) => {
    try {
      const result = await extractSegmentCharactersAction({
        segmentId: segment.id,
      });
      if (result.success) {
        toast.success(result.message || t('novel2video:successMessages.charactersAnalyzed'));
      } else {
        toast.error(result.error || <Trans i18nKey="novel2video:feedback.createAnalysisTaskFailed" />);
      }
    } catch (error) {
      toast.error(<Trans i18nKey="novel2video:feedback.analyzeSegmentCharactersFailed" />);
      console.error(error);
    }
  };

  const handleGenerateSegmentPrompt = async (segment: any) => {
    try {
      const result = await generateSingleSegmentPromptAction({
        segmentId: segment.id,
        style: t('novel2video:styles.realistic'),
        context: t('novel2video:contexts.novelScene'),
      });
      if (result.success) {
        toast.success(result.message || t('novel2video:successMessages.promptGenerated'));
      } else {
        toast.error(<Trans i18nKey="novel2video:feedback.createPromptTaskFailed" />);
      }
    } catch (error) {
      toast.error(<Trans i18nKey="novel2video:errors.generatePromptsFailed" />);
      console.error(error);
    }
  };

  const handleRegenerateSegmentPrompt = async (segment: any) => {
    try {
      const result = await generateSingleSegmentPromptAction({
        segmentId: segment.id,
        style: t('novel2video:styles.realistic'),
        context: t('novel2video:contexts.novelScene'),
      });
      if (result.success) {
        toast.success(result.message || t('novel2video:successMessages.promptRegenerated'));
      } else {
        toast.error(<Trans i18nKey="novel2video:feedback.createRegeneratePromptTaskFailed" />);
      }
    } catch (error) {
      toast.error(<Trans i18nKey="novel2video:errors.regeneratePromptsFailed" />);
      console.error(error);
    }
  };

  const handleGenerateSegmentImage = async (segment: any) => {
    try {
      const result = await generateSingleSegmentImageAction({
        segmentId: segment.id,
      });
      if (result.success) {
        toast.success(result.message || t('novel2video:successMessages.imageGenerated'));
      } else {
        toast.error(<Trans i18nKey="novel2video:feedback.createImageTaskFailed" />);
      }
    } catch (error) {
      toast.error(<Trans i18nKey="novel2video:errors.generateImagesFailed" />);
      console.error(error);
    }
  };

  const handleRegenerateSegmentImage = async (segment: any) => {
    try {
      const result = await generateSingleSegmentImageAction({
        segmentId: segment.id,
      });
      if (result.success) {
        toast.success(result.message || t('novel2video:successMessages.imageRegenerated'));
      } else {
        toast.error(<Trans i18nKey="novel2video:feedback.createRegenerateImageTaskFailed" />);
      }
    } catch (error) {
      toast.error(<Trans i18nKey="novel2video:errors.regenerateImagesFailed" />);
      console.error(error);
    }
  };

  const handleGenerateSegmentAudio = (segment: any) => {
    toast.info(<Trans i18nKey="novel2video:feedback.audioFeatureInDevelopment" />);
  };

  const handleResegmentText = () => {
    setResegmentDialog(true);
  };

  const handleOpenSettings = () => {
    setSettingsDialog(true);
  };

  const getStatusVariant = (
    status: string,
  ): 'default' | 'secondary' | 'destructive' => {
    switch (status) {
      case 'completed':
        return 'default';
      case 'processing_audio':
        return 'secondary';
      case 'processing_image':
        return 'secondary';
      case 'failed':
        return 'destructive';
      default:
        return 'secondary';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed':
        return <Trans i18nKey="novel2video:project.statusTexts.completed" />;
      case 'processing_audio':
        return (
          <Trans i18nKey="novel2video:project.statusTexts.processingAudio" />
        );
      case 'processing_image':
        return (
          <Trans i18nKey="novel2video:project.statusTexts.processingImage" />
        );
      case 'failed':
        return <Trans i18nKey="novel2video:project.statusTexts.failed" />;
      default:
        return <Trans i18nKey="novel2video:project.statusTexts.pending" />;
    }
  };

  // 辅助函数
  const filteredSegments =
    projectData?.segments.filter((segment) => {
      const matchesSearch = segment.text_content
        .toLowerCase()
        .includes(searchTerm.toLowerCase());
      const matchesStatus =
        statusFilter === 'all' || segment.status === statusFilter;
      return matchesSearch && matchesStatus;
    }) || [];

  const paginatedSegments = filteredSegments.slice(
    (currentPage - 1) * segmentsPerPage,
    currentPage * segmentsPerPage,
  );

  const truncateText = (text: string, maxLength: number = 100) => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <Spinner className="h-8 w-8" />
      </div>
    );
  }

  if (!projectData) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <h3 className="mb-2 text-lg font-semibold">
            <Trans i18nKey="novel2video:project.notFound.title" />
          </h3>
          <p className="text-muted-foreground">
            <Trans i18nKey="novel2video:project.notFound.description" />
          </p>
        </div>
      </div>
    );
  }

  const { project, segments } = projectData;

  return (
    <div className="flex h-full flex-col">
      {/* 项目头部 - 紧凑设计 */}
      <ProjectHeader
        project={project}
        segmentsCount={segments.length}
        isProcessing={isProcessing}
        onRefresh={handleRefresh}
        onResegmentText={handleResegmentText}
        onStartProcessing={handleStartProcessing}
        onPauseProcessing={handlePauseProcessing}
        getStatusVariant={getStatusVariant}
        getStatusText={getStatusText}
      />

      {/* 项目进度和操作 */}
      <ProjectProgress
        project={projectData.project}
        segments={projectData.segments}
        characters={projectData.characters}
        onStartProcessing={handleStartProcessing}
        onPauseProcessing={handlePauseProcessing}
        onGeneratePrompts={handleGeneratePrompts}
        onRegenerateAllPrompts={handleRegenerateAllPrompts}
        onGenerateImages={handleGenerateImages}
        onRegenerateAllImages={handleRegenerateAllImages}
        onGenerateAudio={handleGenerateAudio}
        onRegenerateAllAudio={handleRegenerateAllAudio}
        onMergeVideo={handleMergeVideo}
        onExtractCharacters={handleExtractCharacters}
        onAnalyzeSegmentCharacters={handleAnalyzeAllSegmentCharacters}
        onReanalyzeAllSegmentCharacters={handleReanalyzeAllSegmentCharacters}
        isProcessing={isProcessing}
        isGeneratingPrompts={isGeneratingPrompts}
        isGeneratingImages={isGeneratingImages}
        isGeneratingAudio={isGeneratingAudio}
        isExtractingCharacters={isExtractingCharacters}
        isAnalyzingSegments={isAnalyzingSegments}
      />

      {/* 主要内容区域 - 占用剩余所有空间 */}
      <div className="flex-1 min-h-0">
        <Tabs defaultValue="characters" className="flex h-full flex-col">
          <div className="flex flex-shrink-0 items-center justify-between px-4 py-2 border-b">
            <TabsList>
              <TabsTrigger
                value="characters"
                className="flex items-center space-x-2"
              >
                <Users className="h-4 w-4" />
                <span>
                  <Trans i18nKey="novel2video:project.tabs.characters" defaults="人物" />
                </span>
              </TabsTrigger>
              <TabsTrigger
                value="segments"
                className="flex items-center space-x-2"
              >
                <FileTextIcon className="h-4 w-4" />
                <span>
                  <Trans i18nKey="novel2video:project.tabs.segments" />
                </span>
              </TabsTrigger>
              <TabsTrigger
                value="preview"
                className="flex items-center space-x-2"
              >
                <VideoIcon className="h-4 w-4" />
                <span>
                  <Trans i18nKey="novel2video:project.tabs.preview" />
                </span>
              </TabsTrigger>
            </TabsList>

            {/* 设置按钮 */}
            <Button
              variant="outline"
              onClick={handleOpenSettings}
              size="sm"
              className="flex items-center gap-2"
            >
              <Settings className="h-4 w-4" />
              <Trans i18nKey="novel2video:project.tabs.settings" />
            </Button>
          </div>

          <TabsContent value="characters" className="flex-1 overflow-hidden">
            <div className="p-4 h-full">
              <CharactersPanel projectId={projectId} />
            </div>
          </TabsContent>

          <TabsContent
            value="segments"
            className="flex flex-1 flex-col overflow-hidden"
          >
            {/* 工具栏 - 紧凑布局 */}
            <div className="flex-shrink-0 p-4 pb-2">
              <SegmentsToolbar
                searchTerm={searchTerm}
                statusFilter={statusFilter}
                onSearchChange={setSearchTerm}
                onStatusFilterChange={setStatusFilter}
              />
            </div>

            {/* 片段列表 - 占用大部分空间 */}
            <div className="flex-1 min-h-0 overflow-y-auto px-4 custom-scrollbar">
              {projectSettings && projectSettings.image && projectSettings.video ? (
                <OptimizedSegmentsView
                  segments={paginatedSegments}
                  projectSettings={projectSettings}
                  getStatusVariant={getStatusVariant}
                  getStatusText={getStatusText}
                  truncateText={truncateText}
                  onEditSegment={handleEditSegment}
                  onDeleteSegment={handleDeleteSegment}
                  onAnalyzeSegmentCharacters={handleAnalyzeSingleSegmentCharacters}
                  onGenerateSegmentPrompt={handleGenerateSegmentPrompt}
                  onRegenerateSegmentPrompt={handleRegenerateSegmentPrompt}
                  onGenerateSegmentImage={handleGenerateSegmentImage}
                  onRegenerateSegmentImage={handleRegenerateSegmentImage}
                  onGenerateSegmentAudio={handleGenerateSegmentAudio}
                  onUpdate={handleRefresh}
                />
              ) : (
                <div className="flex items-center justify-center py-12">
                  <Spinner className="h-8 w-8" />
                </div>
              )}
            </div>

            {/* 分页 - 固定在底部 */}
            <div className="flex-shrink-0 border-t p-4">
              <SegmentsPagination
                currentPage={currentPage}
                totalItems={filteredSegments.length}
                itemsPerPage={segmentsPerPage}
                onPageChange={setCurrentPage}
              />
            </div>
          </TabsContent>

          <TabsContent value="preview" className="flex-1 overflow-hidden">
            <div className="p-4 h-full">
              <If condition={projectData?.video}>
                <VideoPreview
                  video={projectData.video}
                  onDeleteVideo={handleDeleteVideo}
                  onRegenerateVideo={handleRegenerateVideo}
                  isDeleting={false}
                  isRegenerating={isMergingVideo}
                />
              </If>
              <If condition={!projectData?.video}>
                <div className="flex h-full items-center justify-center">
                  <Card className="w-full max-w-md">
                    <CardHeader>
                      <CardTitle className="flex items-center justify-center gap-2 text-center">
                        <VideoIcon className="h-5 w-5" />
                        <Trans i18nKey="novel2video:project.preview.title" defaults="视频预览" />
                      </CardTitle>
                      <CardDescription className="text-center">
                        <Trans i18nKey="novel2video:project.preview.description" defaults="完成所有步骤后，您可以在这里预览最终视频" />
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="py-8 text-center">
                      <VideoIcon className="text-muted-foreground mx-auto h-12 w-12 mb-4" />
                      <div>
                        <h3 className="mb-2 text-base font-semibold">
                          <Trans i18nKey="novel2video:project.preview.noVideo" defaults="暂无视频" />
                        </h3>
                        <p className="text-muted-foreground text-sm">
                          <Trans i18nKey="novel2video:project.preview.startProcessingMessage" defaults="请先完成所有处理步骤以生成视频" />
                        </p>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </If>
            </div>
          </TabsContent>
        </Tabs>
      </div>

      {/* Dialogs */}
      {editSegmentDialog.open && editSegmentDialog.segment && (
        <EditSegmentDialog
          open={editSegmentDialog.open}
          onOpenChange={(open) =>
            setEditSegmentDialog({
              open,
              segment: open ? editSegmentDialog.segment : null,
            })
          }
          segment={editSegmentDialog.segment}
          onSuccess={(segmentId: string, newTextContent: string) =>
            handleSegmentUpdated(segmentId, newTextContent)
          }
        />
      )}

      {deleteSegmentDialog.open && deleteSegmentDialog.segment && (
        <DeleteSegmentDialog
          open={deleteSegmentDialog.open}
          onOpenChange={(open) =>
            setDeleteSegmentDialog({
              open,
              segment: open ? deleteSegmentDialog.segment : null,
            })
          }
          segment={deleteSegmentDialog.segment}
          onSuccess={(segmentId: string) => handleSegmentDeleted(segmentId)}
        />
      )}

      {resegmentDialog && (
        <ResegmentTextDialog
          open={resegmentDialog}
          onOpenChange={setResegmentDialog}
          project={project}
          onSuccess={handleRefresh}
        />
      )}

      {/* 项目设置模态框 */}
      <ProjectSettings
        open={settingsDialog}
        onOpenChange={setSettingsDialog}
        projectId={projectId}
        currentSettings={projectSettings}
        onSettingsUpdate={setProjectSettings}
      />

      {/* 确认对话框 */}
      <ConfirmDialog
        open={confirmDialog.open}
        onOpenChange={(open) =>
          setConfirmDialog((prev) => ({ ...prev, open }))
        }
        title={confirmDialog.title}
        description={confirmDialog.description}
        onConfirm={confirmDialog.onConfirm}
        variant={confirmDialog.variant}
      />
    </div>
  );
}
