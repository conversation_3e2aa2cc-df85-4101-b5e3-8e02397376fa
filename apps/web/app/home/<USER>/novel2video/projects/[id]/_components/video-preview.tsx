'use client';

import { useState } from 'react';
import { VideoIcon, DownloadIcon, TrashIcon } from 'lucide-react';
import { RefreshCw } from 'lucide-react';
import { Button } from '@kit/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@kit/ui/card';
import { Badge } from '@kit/ui/badge';
import { If } from '@kit/ui/if';
import { Trans } from '@kit/ui/trans';
import { toast } from '@kit/ui/sonner';

interface VideoPreviewProps {
  video: any; // 视频信息
  onDeleteVideo?: () => void;
  onRegenerateVideo?: () => void;
  isDeleting?: boolean;
  isRegenerating?: boolean;
}

// 格式化时长函数
const formatDuration = (seconds: number): string => {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = Math.floor(seconds % 60);
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
};

// 格式化文件大小
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

export function VideoPreview({ video, onDeleteVideo, onRegenerateVideo, isDeleting = false, isRegenerating = false }: VideoPreviewProps) {
  const [isLoading, setIsLoading] = useState(false);

  const handleDownload = () => {
    const videoUrl = `/api/video/${encodeURIComponent(video.file_path)}`;
    const link = document.createElement('a');
    link.href = videoUrl;
    link.download = video.file_path.split('/').pop() || 'video.mp4';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    toast.success('视频下载已开始');
  };

  const videoUrl = `/api/video/${encodeURIComponent(video.file_path)}`;
  const fileFormat = video.file_path.split('.').pop()?.toUpperCase() || 'MP4';

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-lg">
          <VideoIcon className="h-5 w-5 text-blue-600" />
          <Trans i18nKey="novel2video:video.preview.title" defaults="最终视频" />
          <Badge variant="secondary" className="ml-auto">
            {fileFormat}
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* 视频播放器 */}
        <div className="relative bg-black rounded-lg overflow-hidden">
          <video
            controls
            className="w-full h-auto"
            src={videoUrl}
            poster="" // 可以添加视频缩略图
            preload="metadata"
          >
            <Trans i18nKey="novel2video:video.unsupported" defaults="您的浏览器不支持视频播放" />
          </video>
        </div>

        {/* 视频信息 */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
          <div>
            <div className="text-muted-foreground">时长</div>
            <div className="font-medium">
              {formatDuration(video.duration_seconds || 0)}
            </div>
          </div>
          <div>
            <div className="text-muted-foreground">分辨率</div>
            <div className="font-medium">
              {video.width}x{video.height}
            </div>
          </div>
          <div>
            <div className="text-muted-foreground">帧率</div>
            <div className="font-medium">
              {video.frame_rate || 30} FPS
            </div>
          </div>
          <If condition={video.file_size}>
            <div>
              <div className="text-muted-foreground">文件大小</div>
              <div className="font-medium">
                {formatFileSize(video.file_size)}
              </div>
            </div>
          </If>
        </div>

        {/* 操作按钮 */}
        <div className="flex items-center gap-2 pt-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleDownload}
            disabled={isLoading || isRegenerating}
            className="flex items-center gap-2"
          >
            <DownloadIcon className="h-4 w-4" />
            <Trans i18nKey="novel2video:video.download" defaults="下载视频" />
          </Button>

          <If condition={onRegenerateVideo}>
            <Button
              variant="outline"
              size="sm"
              onClick={onRegenerateVideo}
              disabled={isRegenerating || isLoading}
              className="flex items-center gap-2"
            >
              <RefreshCw className={`h-4 w-4 ${isRegenerating ? 'animate-spin' : ''}`} />
              <Trans i18nKey="novel2video:video.regenerate" defaults="重新生成" />
            </Button>
          </If>

          <If condition={onDeleteVideo}>
            <Button
              variant="outline"
              size="sm"
              onClick={onDeleteVideo}
              disabled={isDeleting || isLoading || isRegenerating}
              className="flex items-center gap-2 text-destructive hover:text-destructive"
            >
              <TrashIcon className="h-4 w-4" />
              <Trans i18nKey="novel2video:video.delete" defaults="删除视频" />
            </Button>
          </If>
        </div>

        {/* 创建时间 */}
        <If condition={video.created_at}>
          <div className="text-xs text-muted-foreground">
            创建于: {new Date(video.created_at).toLocaleString()}
          </div>
        </If>
      </CardContent>
    </Card>
  );
}
