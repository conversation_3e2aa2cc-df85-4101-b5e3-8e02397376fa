'use client';

import { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useTranslation } from 'react-i18next';
import {
    Volume2,
    FileText,
    ChevronDown,
    ChevronUp,
    Play,
    Pause
} from 'lucide-react';
import {
    Users,
    Sparkles,
    Zap,
    Eye,
    ImageIcon,
    MoreHorizontal,
    Edit,
    Trash2
} from 'lucide-react';

import { Badge } from '@kit/ui/badge';
import { Button } from '@kit/ui/button';
import { Card, CardContent } from '@kit/ui/card';
import { If } from '@kit/ui/if';
import { Trans } from '@kit/ui/trans';
import { cn } from '@kit/ui/utils';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from '@kit/ui/dropdown-menu';

import { ImageThumbnail } from './image-thumbnail';
import { SegmentStatusSummary } from './enhanced-status-indicators';

interface Segment {
    id: string;
    segment_index: number;
    text_content: string;
    status: string;
    image_prompt?: string | null;
    prompt_generated_at?: string | null;
    prompt_generation_task_id?: string | null;
    audioFile?: any;
    imageFile?: any;
    characters?: Array<{
        id: string;
        name: string;
        aliases?: string[] | null;
        image_prompt?: string | null;
    }>;
    [key: string]: any;
}

interface OptimizedSegmentsViewProps {
    segments: Segment[];
    projectSettings: any;
    getStatusVariant: (status: string) => 'default' | 'secondary' | 'destructive';
    getStatusText: (status: string) => React.ReactNode;
    truncateText: (text: string, maxLength?: number) => string;
    onEditSegment: (segment: Segment) => void;
    onDeleteSegment: (segment: Segment) => void;
    onAnalyzeSegmentCharacters: (segment: Segment) => Promise<void> | void;
    onGenerateSegmentPrompt: (segment: Segment) => Promise<void> | void;
    onRegenerateSegmentPrompt: (segment: Segment) => Promise<void> | void;
    onGenerateSegmentImage: (segment: Segment) => Promise<void> | void;
    onRegenerateSegmentImage: (segment: Segment) => Promise<void> | void;
    onGenerateSegmentAudio: (segment: Segment) => void;
    onUpdate?: () => void;
    searchTerm?: string;
    statusFilter?: string;
}

export function OptimizedSegmentsView({
    segments,
    projectSettings,
    getStatusVariant,
    getStatusText,
    truncateText,
    onEditSegment,
    onDeleteSegment,
    onAnalyzeSegmentCharacters,
    onGenerateSegmentPrompt,
    onRegenerateSegmentPrompt,
    onGenerateSegmentImage,
    onRegenerateSegmentImage,
    onGenerateSegmentAudio,
    onUpdate,
    searchTerm = '',
    statusFilter = 'all'
}: OptimizedSegmentsViewProps) {

    if (segments.length === 0) {
        return (
            <div className="flex flex-col items-center justify-center py-12 text-center">
                <div className="rounded-full bg-muted p-3 mb-4">
                    <FileText className="h-6 w-6 text-muted-foreground" />
                </div>
                <h3 className="text-lg font-medium mb-2">
                    <Trans i18nKey="novel2video:segments.empty.title" defaults="没有找到段落" />
                </h3>
                <p className="text-muted-foreground text-sm max-w-sm">
                    <Trans i18nKey="novel2video:segments.empty.description" defaults="当前筛选条件下没有段落。请尝试调整搜索条件或状态筛选。" />
                </p>
            </div>
        );
    }

    return (
        <div className="space-y-2">
            {segments.map((segment) => (
                <OptimizedSegmentCard
                    key={segment.id}
                    segment={segment}
                    projectSettings={projectSettings}
                    getStatusVariant={getStatusVariant}
                    getStatusText={getStatusText}
                    truncateText={truncateText}
                    onEditSegment={onEditSegment}
                    onDeleteSegment={onDeleteSegment}
                    onAnalyzeSegmentCharacters={onAnalyzeSegmentCharacters}
                    onGenerateSegmentPrompt={onGenerateSegmentPrompt}
                    onRegenerateSegmentPrompt={onRegenerateSegmentPrompt}
                    onGenerateSegmentImage={onGenerateSegmentImage}
                    onRegenerateSegmentImage={onRegenerateSegmentImage}
                    onGenerateSegmentAudio={onGenerateSegmentAudio}
                    onUpdate={onUpdate}
                />
            ))}
        </div>
    );
}

interface OptimizedSegmentCardProps {
    segment: Segment;
    projectSettings: any;
    getStatusVariant: (status: string) => 'default' | 'secondary' | 'destructive';
    getStatusText: (status: string) => React.ReactNode;
    truncateText: (text: string, maxLength?: number) => string;
    onEditSegment: (segment: Segment) => void;
    onDeleteSegment: (segment: Segment) => void;
    onAnalyzeSegmentCharacters: (segment: Segment) => Promise<void> | void;
    onGenerateSegmentPrompt: (segment: Segment) => Promise<void> | void;
    onRegenerateSegmentPrompt: (segment: Segment) => Promise<void> | void;
    onGenerateSegmentImage: (segment: Segment) => Promise<void> | void;
    onRegenerateSegmentImage: (segment: Segment) => Promise<void> | void;
    onGenerateSegmentAudio: (segment: Segment) => void;
    onUpdate?: () => void;
}

function OptimizedSegmentCard({
    segment,
    projectSettings,
    getStatusVariant,
    getStatusText,
    truncateText,
    onEditSegment,
    onDeleteSegment,
    onAnalyzeSegmentCharacters,
    onGenerateSegmentPrompt,
    onRegenerateSegmentPrompt,
    onGenerateSegmentImage,
    onRegenerateSegmentImage,
    onGenerateSegmentAudio,
    onUpdate,
}: OptimizedSegmentCardProps) {
    const { t } = useTranslation('novel2video');
    const [isExpanded, setIsExpanded] = useState(false);
    const [showSparkle, setShowSparkle] = useState(false);
    const [hasCharactersChanged, setHasCharactersChanged] = useState(false);
    const [hasPromptChanged, setHasPromptChanged] = useState(false);

    const charactersRef = useRef<string[]>([]);
    const promptRef = useRef<string | null>(null);

    // 检测变化
    const currentCharacterNames = segment.characters?.map(c => c.name).sort() || [];
    const currentPrompt = segment.image_prompt;

    useEffect(() => {
        const prevCharacterNames = charactersRef.current;
        const prevPrompt = promptRef.current;

        const charactersChanged =
            prevCharacterNames.length > 0 &&
            (prevCharacterNames.length !== currentCharacterNames.length ||
                !prevCharacterNames.every((name, index) => name === currentCharacterNames[index]));

        const promptChanged =
            prevPrompt !== null &&
            prevPrompt !== currentPrompt &&
            currentPrompt !== null;

        if (charactersChanged || promptChanged) {
            setShowSparkle(true);
            setHasCharactersChanged(charactersChanged);
            setHasPromptChanged(promptChanged);

            setTimeout(() => {
                setShowSparkle(false);
                setHasCharactersChanged(false);
                setHasPromptChanged(false);
            }, 2000);
        }

        charactersRef.current = currentCharacterNames;
        promptRef.current = currentPrompt || null;
    }, [currentCharacterNames.join(','), currentPrompt]);

    // 判断是否有详细信息需要展示
    const hasDetails = (segment.characters && segment.characters.length > 0) || segment.image_prompt;
    const hasMedia = segment.audioFile || segment.imageFile;

    return (
        <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className="relative"
        >
            <Card className={cn(
                "relative border transition-all duration-200 hover:shadow-md",
                showSparkle && "ring-2 ring-blue-200 dark:ring-blue-800"
            )}>
                {/* 闪烁指示器 */}
                <AnimatePresence>
                    {showSparkle && (
                        <motion.div
                            className="absolute top-2 right-2 z-10"
                            initial={{ opacity: 0, scale: 0 }}
                            animate={{ opacity: 1, scale: 1 }}
                            exit={{ opacity: 0, scale: 0 }}
                        >
                            <Sparkles className="h-4 w-4 text-yellow-500" />
                        </motion.div>
                    )}
                </AnimatePresence>

                <CardContent className="p-4">
                    {/* 主要信息行 - 紧凑布局 */}
                    <div className="flex items-start gap-3">
                        {/* 序号标识 */}
                        <motion.div
                            className="flex-shrink-0 bg-muted flex h-7 w-7 items-center justify-center rounded-md text-xs font-medium"
                            whileHover={{ scale: 1.05 }}
                        >
                            {segment.segment_index}
                        </motion.div>

                        {/* 文本内容区域 */}
                        <div className="flex-1 min-w-0">
                            <p className="text-sm text-foreground leading-relaxed line-clamp-2 mb-2">
                                {truncateText(segment.text_content, 120)}
                            </p>

                            {/* 紧凑信息栏 */}
                            <div className="flex items-center justify-between">
                                <div className="flex items-center gap-2">
                                    {/* 状态标记 */}
                                    <Badge variant={getStatusVariant(segment.status)} className="text-xs">
                                        {getStatusText(segment.status)}
                                    </Badge>

                                    {/* 状态摘要 */}
                                    <SegmentStatusSummary
                                        segment={segment}
                                        hasCharactersChanged={hasCharactersChanged}
                                        hasPromptChanged={hasPromptChanged}
                                    />
                                </div>

                                {/* 操作区域 */}
                                <div className="flex items-center gap-1">
                                    {/* 图片缩略图 */}
                                    <ImageThumbnail imageFile={segment.imageFile} size="sm" />

                                    {/* 音频预览按钮 */}
                                    <If condition={segment.audioFile}>
                                        <AudioPlayer audioFile={segment.audioFile} size="sm" />
                                    </If>

                                    {/* 展开/收起按钮 */}
                                    {hasDetails && (
                                        <Button
                                            variant="ghost"
                                            size="sm"
                                            onClick={() => setIsExpanded(!isExpanded)}
                                            className="h-7 w-7 p-0"
                                        >
                                            {isExpanded ? (
                                                <ChevronUp className="h-3 w-3" />
                                            ) : (
                                                <ChevronDown className="h-3 w-3" />
                                            )}
                                        </Button>
                                    )}

                                    {/* 操作菜单 */}
                                    <DropdownMenu>
                                        <DropdownMenuTrigger asChild>
                                            <Button variant="ghost" size="sm" className="h-7 w-7 p-0">
                                                <MoreHorizontal className="h-3 w-3" />
                                            </Button>
                                        </DropdownMenuTrigger>
                                        <DropdownMenuContent align="end">
                                            {/* Basic operations */}
                                            <DropdownMenuItem onClick={() => onEditSegment(segment)}>
                                                <Edit className="h-3 w-3 mr-2" />
                                                <Trans i18nKey="novel2video:segments.actions.edit" />
                                            </DropdownMenuItem>

                                            {/* Character analysis */}
                                            <DropdownMenuItem onClick={() => onAnalyzeSegmentCharacters(segment)}>
                                                <Users className="h-3 w-3 mr-2" />
                                                {segment.characters && segment.characters.length > 0 ?
                                                    <Trans i18nKey="novel2video:segments.actions.reanalyzeCharacters" /> :
                                                    <Trans i18nKey="novel2video:segments.actions.analyzeCharacters" />
                                                }
                                            </DropdownMenuItem>

                                            {/* Prompt generation */}
                                            {segment.image_prompt ? (
                                                <DropdownMenuItem onClick={() => onRegenerateSegmentPrompt(segment)}>
                                                    <FileText className="h-3 w-3 mr-2" />
                                                    <Trans i18nKey="novel2video:segments.actions.regeneratePrompt" />
                                                </DropdownMenuItem>
                                            ) : (
                                                <DropdownMenuItem onClick={() => onGenerateSegmentPrompt(segment)}>
                                                    <FileText className="h-3 w-3 mr-2" />
                                                    <Trans i18nKey="novel2video:segments.actions.generatePrompt" />
                                                </DropdownMenuItem>
                                            )}

                                            {/* Image generation */}
                                            {segment.image_prompt && (
                                                <>
                                                    {segment.imageFile ? (
                                                        <DropdownMenuItem onClick={() => onRegenerateSegmentImage(segment)}>
                                                            <ImageIcon className="h-3 w-3 mr-2" />
                                                            <Trans i18nKey="novel2video:segments.actions.regenerateImage" />
                                                        </DropdownMenuItem>
                                                    ) : (
                                                        <DropdownMenuItem onClick={() => onGenerateSegmentImage(segment)}>
                                                            <ImageIcon className="h-3 w-3 mr-2" />
                                                            <Trans i18nKey="novel2video:segments.actions.generateImage" />
                                                        </DropdownMenuItem>
                                                    )}
                                                </>
                                            )}

                                            {/* Audio generation */}
                                            {!segment.audioFile && (
                                                <DropdownMenuItem onClick={() => onGenerateSegmentAudio(segment)}>
                                                    <Volume2 className="h-3 w-3 mr-2" />
                                                    <Trans i18nKey="novel2video:segments.actions.generateAudio" />
                                                </DropdownMenuItem>
                                            )}

                                            {/* Audio playback */}
                                            {segment.audioFile && (
                                                <DropdownMenuItem onClick={() => {
                                                    const audioUrl = `/api/audio/${encodeURIComponent(segment.audioFile.file_path)}`;
                                                    const audio = new Audio(audioUrl);
                                                    audio.play().catch(console.error);
                                                }}>
                                                    <Play className="h-3 w-3 mr-2" />
                                                    <Trans i18nKey="novel2video:segments.actions.playAudio" />
                                                </DropdownMenuItem>
                                            )}

                                            {/* Delete operation */}
                                            <DropdownMenuItem
                                                onClick={() => onDeleteSegment(segment)}
                                                className="text-destructive"
                                            >
                                                <Trash2 className="h-3 w-3 mr-2" />
                                                <Trans i18nKey="novel2video:segments.actions.delete" />
                                            </DropdownMenuItem>
                                        </DropdownMenuContent>
                                    </DropdownMenu>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* 详细信息展开区域 */}
                    <AnimatePresence>
                        {isExpanded && hasDetails && (
                            <motion.div
                                initial={{ height: 0, opacity: 0 }}
                                animate={{ height: "auto", opacity: 1 }}
                                exit={{ height: 0, opacity: 0 }}
                                transition={{ duration: 0.3 }}
                                className="overflow-hidden"
                            >
                                <div className="mt-3 pt-3 border-t space-y-3">
                                    {/* 人物详细信息 */}
                                    {segment.characters && segment.characters.length > 0 && (
                                        <div>
                                            <div className="flex items-center gap-2 mb-2">
                                                <Users className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                                                <span className="text-sm font-medium text-blue-600 dark:text-blue-400">
                                                    <Trans i18nKey="novel2video:segments.details.characters" />
                                                </span>
                                                {hasCharactersChanged && (
                                                    <Zap className="h-3 w-3 text-green-500" />
                                                )}
                                            </div>
                                            <div className="flex flex-wrap gap-1">
                                                {segment.characters.map((character) => (
                                                    <div key={character.id} className="space-y-1">
                                                        <Badge
                                                            variant="secondary"
                                                            className="text-xs bg-blue-50 text-blue-700 hover:bg-blue-100 dark:bg-blue-900/30 dark:text-blue-300"
                                                        >
                                                            {character.name}
                                                        </Badge>
                                                        {character.aliases && character.aliases.length > 0 && (
                                                            <div className="flex flex-wrap gap-1">
                                                                {character.aliases.map((alias, index) => (
                                                                    <span
                                                                        key={`${character.id}-${alias}-${index}`}
                                                                        className="inline-flex items-center px-1.5 py-0.5 rounded text-xs bg-gray-50 text-gray-600 border border-gray-200 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-700"
                                                                    >
                                                                        {alias}
                                                                    </span>
                                                                ))}
                                                            </div>
                                                        )}
                                                    </div>
                                                ))}
                                            </div>
                                        </div>
                                    )}

                                    {/* 图片提示词 */}
                                    {segment.image_prompt && (
                                        <div>
                                            <div className="flex items-center gap-2 mb-2">
                                                <FileText className="h-4 w-4 text-purple-600 dark:text-purple-400" />
                                                <span className="text-sm font-medium text-purple-600 dark:text-purple-400">
                                                    <Trans i18nKey="novel2video:segments.details.imagePrompt" />
                                                </span>
                                                {hasPromptChanged && (
                                                    <Eye className="h-3 w-3 text-green-500" />
                                                )}
                                            </div>
                                            <motion.div
                                                className="text-sm text-muted-foreground bg-muted/50 rounded-lg p-3 leading-relaxed"
                                                animate={hasPromptChanged ? {
                                                    backgroundColor: [
                                                        "rgba(139, 92, 246, 0.05)",
                                                        "rgba(139, 92, 246, 0.15)",
                                                        "rgba(139, 92, 246, 0.05)"
                                                    ]
                                                } : {}}
                                                transition={{ duration: 1.5 }}
                                            >
                                                {segment.image_prompt}
                                            </motion.div>
                                        </div>
                                    )}

                                    {/* Audio preview */}
                                    {segment.audioFile && (
                                        <div>
                                            <div className="flex items-center gap-2 mb-2">
                                                <Volume2 className="h-4 w-4 text-purple-600 dark:text-purple-400" />
                                                <span className="text-sm font-medium text-purple-600 dark:text-purple-400">
                                                    <Trans i18nKey="novel2video:segments.details.audioPreview" />
                                                </span>
                                            </div>
                                            <div className="bg-muted/30 rounded-lg p-3">
                                                <AudioPlayer audioFile={segment.audioFile} size="md" />
                                            </div>
                                        </div>
                                    )}
                                </div>
                            </motion.div>
                        )}
                    </AnimatePresence>
                </CardContent>
            </Card>
        </motion.div>
    );
}

// Audio player component
function AudioPlayer({ audioFile, size = 'sm' }: { audioFile: any; size?: 'sm' | 'md' }) {
    const { t } = useTranslation('novel2video');
    const [isPlaying, setIsPlaying] = useState(false);
    const [currentTime, setCurrentTime] = useState(0);
    const [duration, setDuration] = useState(0);
    const audioRef = useRef<HTMLAudioElement>(null);

    // 构建音频URL
    const audioUrl = `/api/audio/${encodeURIComponent(audioFile.file_path)}`;

    useEffect(() => {
        const audio = audioRef.current;
        if (!audio) return;

        const handleTimeUpdate = () => setCurrentTime(audio.currentTime);
        const handleLoadedMetadata = () => setDuration(audio.duration);
        const handleEnded = () => setIsPlaying(false);
        const handleError = (e: any) => {
            console.error('Audio error:', e);
            setIsPlaying(false);
        };

        audio.addEventListener('timeupdate', handleTimeUpdate);
        audio.addEventListener('loadedmetadata', handleLoadedMetadata);
        audio.addEventListener('ended', handleEnded);
        audio.addEventListener('error', handleError);

        return () => {
            audio.removeEventListener('timeupdate', handleTimeUpdate);
            audio.removeEventListener('loadedmetadata', handleLoadedMetadata);
            audio.removeEventListener('ended', handleEnded);
            audio.removeEventListener('error', handleError);
        };
    }, [audioUrl]);

    const togglePlay = async () => {
        const audio = audioRef.current;
        if (!audio) return;

        try {
            if (isPlaying) {
                audio.pause();
                setIsPlaying(false);
            } else {
                // 确保音频已加载
                if (audio.readyState < 2) {
                    await new Promise((resolve) => {
                        const handleCanPlay = () => {
                            audio.removeEventListener('canplay', handleCanPlay);
                            resolve(undefined);
                        };
                        audio.addEventListener('canplay', handleCanPlay);
                    });
                }

                await audio.play();
                setIsPlaying(true);
            }
        } catch (error) {
            console.error('Audio play error:', error);
            setIsPlaying(false);
        }
    };

    const formatTime = (time: number) => {
        const minutes = Math.floor(time / 60);
        const seconds = Math.floor(time % 60);
        const secondsStr = seconds < 10 ? `0${seconds}` : `${seconds}`;
        return `${minutes}:${secondsStr}`;
    };

    return (
        <div className="flex items-center gap-2">
            <audio
                ref={audioRef}
                src={audioUrl}
                preload="metadata"
            />

            <Button
                variant="ghost"
                size="sm"
                onClick={togglePlay}
                className={cn(
                    "p-0",
                    size === "sm" ? "h-7 w-7" : "h-8 w-8"
                )}
                title={isPlaying ? t('segments.actions.pauseAudio') : t('segments.actions.playAudio')}
            >
                {isPlaying ? (
                    <Pause className="h-3 w-3" />
                ) : (
                    <Play className="h-3 w-3" />
                )}
            </Button>

            {size === 'md' && duration > 0 && (
                <>
                    <div className="flex-1 min-w-0">
                        <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1">
                            <div
                                className="bg-blue-500 h-1 rounded-full transition-all duration-100"
                                style={{ width: `${(currentTime / duration) * 100}%` }}
                            />
                        </div>
                    </div>
                    <span className="text-xs text-muted-foreground whitespace-nowrap">
                        {formatTime(currentTime)} / {formatTime(duration)}
                    </span>
                </>
            )}
        </div>
    );
} 