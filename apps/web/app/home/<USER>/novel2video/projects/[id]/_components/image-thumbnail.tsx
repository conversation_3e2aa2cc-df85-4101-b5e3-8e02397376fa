'use client';

import { useState } from 'react';
import Image from 'next/image';
import { Dialog, DialogContent } from '@kit/ui/dialog';
import { ImageIcon } from 'lucide-react';
import { Loader2 } from 'lucide-react';
import { If } from '@kit/ui/if';
import { Button } from '@kit/ui/button';
import { Trans } from '@kit/ui/trans';
import { cn } from '@kit/ui/utils';

interface ImageThumbnailProps {
  imageFile: {
    file_path: string;
    width?: number;
    height?: number;
    image_prompt?: string;
  } | null;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
}

export function ImageThumbnail({ imageFile, className = '', size = 'sm' }: ImageThumbnailProps) {
  const [isViewerOpen, setIsViewerOpen] = useState(false);
  const [imageError, setImageError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  if (!imageFile || imageError) {
    const sizeClasses = {
      sm: 'h-7 w-7',
      md: 'h-9 w-9',
      lg: 'h-14 w-14'
    };

    return (
      <div className={cn(
        'flex items-center justify-center rounded-lg bg-purple-50 dark:bg-purple-900/30 border border-purple-200 dark:border-purple-800/50',
        sizeClasses[size],
        className
      )}>
        <ImageIcon className="h-3.5 w-3.5 text-purple-600 dark:text-purple-400" />
      </div>
    );
  }

  const sizeClasses = {
    sm: 'h-7 w-7',
    md: 'h-9 w-9',
    lg: 'h-14 w-14'
  };

  // 构建图片URL
  const imageUrl = `/api/image/${encodeURIComponent(imageFile.file_path)}`;

  return (
    <>
      <div
        className={cn(
          'group relative cursor-pointer overflow-hidden rounded-lg border border-border/50 bg-muted/30',
          'hover:border-primary/30 hover:ring-1 hover:ring-primary/20 transition-all duration-200',
          sizeClasses[size],
          className
        )}
        onClick={() => setIsViewerOpen(true)}
      >
        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center bg-muted/30">
            <Loader2 className="h-3.5 w-3.5 animate-spin text-muted-foreground" />
          </div>
        )}
        <Image
          src={imageUrl}
          alt="Generated image"
          fill
          className={cn(
            'object-cover transition-all duration-300',
            'group-hover:scale-110',
            isLoading ? 'opacity-0' : 'opacity-100'
          )}
          onError={() => setImageError(true)}
          onLoadingComplete={() => setIsLoading(false)}
          sizes="(max-width: 768px) 32px, 48px"
        />
      </div>

      {/* 图片查看器模态框 */}
      <Dialog open={isViewerOpen} onOpenChange={setIsViewerOpen}>
        <DialogContent className="max-w-5xl p-0 overflow-hidden">
          <div className="relative">
            <div className="relative aspect-[4/3] max-h-[80vh]">
              <Image
                src={imageUrl}
                alt="Generated image"
                fill
                className="object-contain"
                onError={() => setImageError(true)}
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 80vw, 1000px"
                priority
              />
            </div>

            <If condition={imageFile.image_prompt}>
              <div className="p-4 border-t bg-card/95 backdrop-blur-sm">
                <div className="flex items-center justify-between mb-2">
                  <h3 className="text-sm font-medium">
                    <Trans i18nKey="novel2video:imageViewer.prompt" defaults="生成提示词" />
                  </h3>
                  <If condition={imageFile.width && imageFile.height}>
                    <span className="text-xs text-muted-foreground">
                      {imageFile.width} × {imageFile.height}
                    </span>
                  </If>
                </div>
                <p className="text-sm text-muted-foreground leading-relaxed">
                  {imageFile.image_prompt}
                </p>
              </div>
            </If>

            <Button
              variant="outline"
              size="sm"
              className="absolute right-4 top-4 bg-background/80 backdrop-blur-sm hover:bg-background"
              onClick={() => setIsViewerOpen(false)}
            >
              <Trans i18nKey="common:close" defaults="关闭" />
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}