import { AppBreadcrumbs } from '@kit/ui/app-breadcrumbs';
import { PageBody, PageHeader } from '@kit/ui/page';
import { Trans } from '@kit/ui/trans';

import { createI18nServerInstance } from '~/lib/i18n/i18n.server';
import { withI18n } from '~/lib/i18n/with-i18n';

import { Novel2VideoDashboard } from './_components/novel2video-dashboard';

export const generateMetadata = async () => {
  const i18n = await createI18nServerInstance();
  const title = i18n.t('novel2video:pageTitle');

  return {
    title,
  };
};

function Novel2VideoPage() {
  return (
    <div className="min-h-screen bg-background">
      <div className="relative">
        {/* Subtle background pattern using CSS variables */}
        <div className="absolute inset-0 bg-gradient-to-br from-background via-muted/20 to-background" />

        <div className="relative">
          <PageHeader
            title={<Trans i18nKey={'novel2video:pageTitle'} />}
            description={<AppBreadcrumbs />}
          />

          <PageBody>
            <Novel2VideoDashboard />
          </PageBody>
        </div>
      </div>
    </div>
  );
}

export default withI18n(Novel2VideoPage);
