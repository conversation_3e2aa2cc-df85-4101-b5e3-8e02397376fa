'use client';

import { useState } from 'react';

import {
  PlusIcon,
  VideoIcon,
} from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

import { Button } from '@kit/ui/button';
import { If } from '@kit/ui/if';
import { Trans } from '@kit/ui/trans';

import { CreateProjectDialog } from './create-project-dialog';
import { ProjectsList } from './projects-list';

export function Novel2VideoDashboard() {
  const [showCreateDialog, setShowCreateDialog] = useState(false);

  return (
    <motion.div
      className="flex h-full flex-col space-y-8"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      {/* Enhanced Header Section */}
      <motion.div
        className="relative"
        initial={{ y: -20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.6, ease: "easeOut" }}
      >
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6 p-6 rounded-xl border bg-card/50 backdrop-blur-sm shadow-sm">
          <motion.div
            className="flex items-center space-x-4"
            initial={{ x: -20, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.1 }}
          >
            <motion.div
              className="flex h-12 w-12 items-center justify-center rounded-xl bg-primary/10 ring-1 ring-primary/20"
              whileHover={{
                scale: 1.1,
                backgroundColor: 'hsl(var(--primary) / 0.15)'
              }}
              transition={{ duration: 0.3 }}
            >
              <VideoIcon className="h-6 w-6 text-primary" />
            </motion.div>
            <div className="space-y-1">
              <h1 className="text-2xl font-bold tracking-tight">
                <Trans i18nKey="novel2video:dashboard.title" />
              </h1>
              <p className="text-sm text-muted-foreground">
                <Trans i18nKey="novel2video:dashboard.subtitle" />
              </p>
            </div>
          </motion.div>

          <motion.div
            initial={{ x: 20, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <Button
              onClick={() => setShowCreateDialog(true)}
              size="lg"
              className="shadow-lg hover:shadow-xl transition-all duration-300"
              data-test="create-project-button"
            >
              <motion.div
                className="flex items-center space-x-2"
                whileHover={{ x: 2 }}
                transition={{ duration: 0.2 }}
              >
                <motion.div
                  animate={{ rotate: showCreateDialog ? 45 : 0 }}
                  transition={{ duration: 0.2 }}
                >
                  <PlusIcon className="h-4 w-4" />
                </motion.div>
                <span><Trans i18nKey="novel2video:dashboard.createProject" /></span>
              </motion.div>
            </Button>
          </motion.div>
        </div>
      </motion.div>

      {/* Main content area */}
      <motion.div
        className="flex-1 overflow-hidden"
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.7, delay: 0.3 }}
      >
        <div className="container h-full px-6 py-6">
          <motion.div
            initial={{ scale: 0.95, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.4 }}
          >
            <ProjectsList />
          </motion.div>
        </div>
      </motion.div>

      {/* Create project dialog */}
      <AnimatePresence mode="wait">
        <If condition={showCreateDialog}>
          <CreateProjectDialog
            open={showCreateDialog}
            onOpenChange={setShowCreateDialog}
          />
        </If>
      </AnimatePresence>
    </motion.div>
  );
}

