'use client';

import { useState, useTransition, useCallback, useRef } from 'react';

import { zodResolver } from '@hookform/resolvers/zod';
import {
  FileTextIcon,
  UploadIcon,
  CrossIcon,
  InfoIcon,
  BookIcon,
  PlusIcon,
} from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { z } from 'zod';


import { Button } from '@kit/ui/button';
import { Badge } from '@kit/ui/badge';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@kit/ui/dialog';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@kit/ui/form';
import { Input } from '@kit/ui/input';
import { Progress } from '@kit/ui/progress';
import { toast } from '@kit/ui/sonner';
import { Spinner } from '@kit/ui/spinner';
import { Textarea } from '@kit/ui/textarea';
import { Trans } from '@kit/ui/trans';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@kit/ui/tabs';

import { createProjectAction } from '../_lib/server/server-actions';

type CreateProjectFormData = {
  title: string;
  description?: string;
  text: string;
};

interface CreateProjectDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function CreateProjectDialog({
  open,
  onOpenChange,
}: CreateProjectDialogProps) {
  const { t } = useTranslation('novel2video');
  const [isPending, startTransition] = useTransition();
  const [currentStep, setCurrentStep] = useState(0);
  const [textFile, setTextFile] = useState<File | null>(null);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [inputMethod, setInputMethod] = useState<'text' | 'file'>('text');
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Form validation schema with i18n
  const CreateProjectSchema = z.object({
    title: z
      .string()
      .min(1, t('validation.titleRequired'))
      .max(255, t('validation.titleTooLong')),
    description: z.string().optional(),
    text: z
      .string()
      .min(10, t('validation.textMinLength', { min: 10 }))
      .max(50000, t('validation.textMaxLength', { max: 50000 })),
  });

  // Step configuration with i18n
  const steps = [
    { id: 'info', title: t('createProject.steps.info'), icon: BookIcon },
    { id: 'content', title: t('createProject.steps.content'), icon: FileTextIcon },
  ];

  const form = useForm<CreateProjectFormData>({
    resolver: zodResolver(CreateProjectSchema),
    defaultValues: {
      title: '',
      description: '',
      text: '',
    },
  });

  // File validation
  const validateFile = useCallback((file: File): boolean => {
    if (!file.type.includes('text') && !file.name.endsWith('.txt')) {
      toast.error(t('createProject.errors.invalidFileType'));
      return false;
    }

    if (file.size > 5 * 1024 * 1024) {
      toast.error(t('createProject.errors.fileTooLarge'));
      return false;
    }

    return true;
  }, [t]);

  // File upload handling
  const handleFileUpload = useCallback((file: File) => {
    if (!validateFile(file)) return;

    setTextFile(file);
    setUploadProgress(0);
    setInputMethod('file');

    // Simulate upload progress
    const progressInterval = setInterval(() => {
      setUploadProgress(prev => {
        if (prev >= 100) {
          clearInterval(progressInterval);
          return 100;
        }
        return prev + 20;
      });
    }, 100);

    // Read file content
    const reader = new FileReader();
    reader.onload = (e) => {
      const content = e.target?.result as string;
      form.setValue('text', content, { shouldValidate: true });

      // If no title, use filename as title
      if (!form.getValues('title')) {
        const filename = file.name.replace(/\.[^/.]+$/, '');
        form.setValue('title', filename);
      }

      toast.success(t('createProject.fileUploadSuccess'));
    };
    reader.readAsText(file, 'UTF-8');
  }, [form, validateFile, t]);

  // File input handling
  const handleFileInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      handleFileUpload(file);
    }
  };

  // Remove file
  const removeFile = useCallback(() => {
    setTextFile(null);
    setUploadProgress(0);
    form.setValue('text', '', { shouldValidate: true });
    setInputMethod('text');
  }, [form]);

  // Reset form
  const resetForm = useCallback(() => {
    form.reset();
    setTextFile(null);
    setUploadProgress(0);
    setCurrentStep(0);
    setInputMethod('text');
  }, [form]);

  // Validate current step completion
  const canProceed = () => {
    const values = form.getValues();
    if (currentStep === 0) {
      return !!values.title?.trim();
    } else if (currentStep === 1) {
      return !!values.text?.trim() && values.text.length >= 10;
    }
    return false;
  };

  // Next step
  const goNext = async () => {
    if (currentStep === 0) {
      const isValid = await form.trigger(['title', 'description']);
      if (isValid) {
        setCurrentStep(1);
      }
    }
  };

  // Previous step
  const goBack = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  // Form submission
  const onSubmit = (data: CreateProjectFormData) => {
    startTransition(async () => {
      try {
        const result = await createProjectAction({
          title: data.title,
          description: data.description,
          text: data.text,
        });

        if (result.success) {
          toast.success(t('createProject.success'));
          onOpenChange(false);
          resetForm();
          window.location.reload();
        } else {
          toast.error(result.message || t('createProject.errors.createFailed'));
        }
      } catch (error) {
        console.error('Error creating project:', error);
        toast.error(t('createProject.errors.createFailed'));
      }
    });
  };

  // Get character count color
  const getCharCountColor = (count: number) => {
    if (count < 10) return 'text-destructive';
    if (count > 45000) return 'text-orange-500';
    return 'text-muted-foreground';
  };

  // Render step content
  const renderStepContent = () => {
    if (currentStep === 0) {
      // Step 1: Project information
      return (
        <motion.div
          key="info"
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -10 }}
          transition={{ duration: 0.2 }}
          className="space-y-4"
        >
          <FormField
            control={form.control}
            name="title"
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  <Trans i18nKey="novel2video:createProject.titleLabel" />
                </FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    data-test="project-title-input"
                    placeholder={t('createProject.titlePlaceholder')}
                  />
                </FormControl>
                <FormDescription>
                  <Trans i18nKey="novel2video:createProject.titleDescription" />
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="description"
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  <Trans i18nKey="novel2video:createProject.descriptionLabel" />
                </FormLabel>
                <FormControl>
                  <Textarea
                    className="min-h-[100px] resize-none"
                    {...field}
                    data-test="project-description-input"
                    placeholder={t('createProject.descriptionPlaceholder')}
                  />
                </FormControl>
                <FormDescription>
                  <Trans i18nKey="novel2video:createProject.descriptionHint" />
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </motion.div>
      );
    } else if (currentStep === 1) {
      // Step 2: Text content
      return (
        <motion.div
          key="content"
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -10 }}
          transition={{ duration: 0.2 }}
          className="space-y-4"
        >
          <div className="flex items-center justify-between">
            <FormLabel>
              <Trans i18nKey="novel2video:createProject.textLabel" />
            </FormLabel>
            {textFile && (
              <Badge variant="secondary" className="gap-1">
                <FileTextIcon className="h-3 w-3" />
                {textFile.name}
              </Badge>
            )}
          </div>

          <Tabs value={inputMethod} onValueChange={(v) => setInputMethod(v as 'text' | 'file')}>
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="text" className="gap-2">
                <BookIcon className="h-4 w-4" />
                <Trans i18nKey="novel2video:createProject.directInput" />
              </TabsTrigger>
              <TabsTrigger value="file" className="gap-2">
                <UploadIcon className="h-4 w-4" />
                <Trans i18nKey="novel2video:createProject.fileUpload" />
              </TabsTrigger>
            </TabsList>

            <TabsContent value="text" className="mt-4">
              <FormField
                control={form.control}
                name="text"
                render={({ field }) => {
                  const charCount = field.value?.length || 0;
                  const charCountColor = getCharCountColor(charCount);

                  return (
                    <FormItem>
                      <div className="relative">
                        <FormControl>
                          <Textarea
                            className="min-h-[300px] font-mono text-sm resize-none pr-20"
                            {...field}
                            data-test="project-text-input"
                            placeholder={t('createProject.textPlaceholder')}
                          />
                        </FormControl>

                        {/* Character count */}
                        <div className={`absolute bottom-2 right-2 text-sm font-medium ${charCountColor}`}>
                          {charCount.toLocaleString()} / 50,000
                        </div>
                      </div>
                      <FormMessage />
                    </FormItem>
                  );
                }}
              />
            </TabsContent>

            <TabsContent value="file" className="mt-4 space-y-4">
              {textFile ? (
                <motion.div
                  initial={{ opacity: 0, scale: 0.95 }}
                  animate={{ opacity: 1, scale: 1 }}
                  className="relative bg-muted/50 rounded-lg p-4 border"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-primary/10 rounded-lg">
                        <FileTextIcon className="h-5 w-5 text-primary" />
                      </div>
                      <div>
                        <p className="font-medium">{textFile.name}</p>
                        <p className="text-sm text-muted-foreground">
                          {(textFile.size / 1024).toFixed(1)} KB
                        </p>
                      </div>
                    </div>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={removeFile}
                    >
                      <CrossIcon className="h-4 w-4" />
                    </Button>
                  </div>
                  {uploadProgress < 100 && (
                    <div className="mt-3">
                      <Progress value={uploadProgress} className="h-1" />
                    </div>
                  )}
                </motion.div>
              ) : (
                <div>
                  <Button
                    type="button"
                    variant="outline"
                    className="w-full h-32 border-dashed"
                    onClick={() => fileInputRef.current?.click()}
                  >
                    <div className="flex flex-col items-center gap-2">
                      <UploadIcon className="h-8 w-8 text-muted-foreground" />
                      <div className="text-center">
                        <p className="font-medium">
                          <Trans i18nKey="novel2video:createProject.uploadPrompt" />
                        </p>
                        <p className="text-sm text-muted-foreground">
                          {t('createProject.supportedFiles')}
                        </p>
                      </div>
                    </div>
                  </Button>
                  <input
                    ref={fileInputRef}
                    type="file"
                    accept=".txt,text/*"
                    onChange={handleFileInputChange}
                    className="hidden"
                    data-test="text-file-input"
                  />
                </div>
              )}

              {/* File content preview */}
              {textFile && form.watch('text') && (
                <div className="space-y-2">
                  <p className="text-sm font-medium text-muted-foreground">
                    <Trans i18nKey="novel2video:createProject.contentPreview" />
                  </p>
                  <div className="bg-muted/30 rounded p-3 max-h-32 overflow-y-auto">
                    <p className="text-sm font-mono whitespace-pre-wrap line-clamp-5">
                      {form.watch('text').substring(0, 300)}...
                    </p>
                  </div>
                </div>
              )}
            </TabsContent>
          </Tabs>

          <FormDescription className="flex items-start gap-2">
            <InfoIcon className="h-4 w-4 mt-0.5 flex-shrink-0" />
            <span className="text-xs">
              <Trans i18nKey="novel2video:createProject.contentHint" />
            </span>
          </FormDescription>
        </motion.div>
      );
    }

    return null;
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>
            <Trans i18nKey="novel2video:dashboard.createProject" />
          </DialogTitle>
          <DialogDescription>
            <Trans i18nKey="novel2video:createProject.dialogDescription" />
          </DialogDescription>
        </DialogHeader>

        {/* Progress indicator */}
        <div className="flex items-center justify-center py-4">
          {steps.map((step, index) => {
            const Icon = step.icon;
            const isActive = index === currentStep;
            const isCompleted = index < currentStep;

            return (
              <div key={step.id} className="flex items-center">
                <motion.div
                  initial={false}
                  animate={{
                    scale: isActive ? 1.1 : 1,
                    opacity: isActive || isCompleted ? 1 : 0.5,
                  }}
                  className="relative flex items-center"
                >
                  <div
                    className={`
                      w-10 h-10 rounded-full flex items-center justify-center transition-colors
                      ${isActive ? 'bg-primary text-primary-foreground' :
                        isCompleted ? 'bg-primary/20 text-primary' :
                          'bg-muted text-muted-foreground'}
                    `}
                  >
                    <Icon className="h-5 w-5" />
                  </div>
                  {isActive && (
                    <motion.div
                      className="absolute inset-0 rounded-full border-2 border-primary"
                      initial={{ scale: 0.8, opacity: 0 }}
                      animate={{ scale: 1.2, opacity: 0 }}
                      transition={{ duration: 1, repeat: Infinity }}
                    />
                  )}
                  <span className={`ml-2 text-sm font-medium ${isActive ? 'text-foreground' : 'text-muted-foreground'
                    }`}>
                    {step.title}
                  </span>
                </motion.div>
                {index < steps.length - 1 && (
                  <div className={`mx-4 w-16 h-0.5 transition-colors ${isCompleted ? 'bg-primary' : 'bg-muted'
                    }`} />
                )}
              </div>
            );
          })}
        </div>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Step content */}
            <AnimatePresence mode="wait">
              {renderStepContent()}
            </AnimatePresence>

            {/* Action buttons */}
            <div className="flex items-center justify-between pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={currentStep === 0 ? () => { onOpenChange(false); resetForm(); } : goBack}
                disabled={isPending}
              >
                {currentStep === 0 ? (
                  <Trans i18nKey="common:cancel" />
                ) : (
                  <Trans i18nKey="common:back" />
                )}
              </Button>

              {currentStep === 0 ? (
                <Button
                  type="button"
                  onClick={goNext}
                  disabled={!canProceed()}
                >
                  <Trans i18nKey="common:next" />
                </Button>
              ) : (
                <Button
                  type="submit"
                  disabled={isPending || !canProceed()}
                  data-test="create-project-submit"
                >
                  {isPending ? (
                    <>
                      <Spinner className="mr-2 h-4 w-4" />
                      <Trans i18nKey="novel2video:createProject.creating" />
                    </>
                  ) : (
                    <>
                      <PlusIcon className="mr-2 h-4 w-4" />
                      <Trans i18nKey="novel2video:createProject.createButton" />
                    </>
                  )}
                </Button>
              )}
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}