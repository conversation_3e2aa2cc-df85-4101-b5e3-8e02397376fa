'use client';

import { useEffect, useState } from 'react';

import Link from 'next/link';

import {
  CalendarIcon,
  MoreHorizontalIcon,
  FileTextIcon,
  PlayIcon,
  VideoIcon,
  CheckCircleIcon,
  AlertTriangle,
  ClockIcon,
} from 'lucide-react';
import { motion } from 'framer-motion';

import { useUserWorkspace } from '@kit/accounts/hooks/use-user-workspace';
import { Badge } from '@kit/ui/badge';
import { Button } from '@kit/ui/button';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@kit/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@kit/ui/dropdown-menu';
import { If } from '@kit/ui/if';
import { Progress } from '@kit/ui/progress';
import { Skeleton } from '@kit/ui/skeleton';
import { Trans } from '@kit/ui/trans';

import { getProjectsAction } from '../_lib/server/server-actions';

type ProjectData = {
  id: string;
  title: string;
  description: string | null;
  status: 'draft' | 'processing' | 'completed' | 'failed';
  progress: number;
  created_at: string;
  segmentCount?: number;
};

const statusConfig = {
  draft: {
    label: 'novel2video:status.project.draft',
    variant: 'secondary' as const,
    icon: FileTextIcon,
    color: 'text-muted-foreground',
  },
  processing: {
    label: 'novel2video:status.project.processing',
    variant: 'default' as const,
    icon: ClockIcon,
    color: 'text-blue-600',
  },
  completed: {
    label: 'novel2video:status.project.completed',
    variant: 'default' as const,
    icon: CheckCircleIcon,
    color: 'text-green-600',
  },
  failed: {
    label: 'novel2video:status.project.failed',
    variant: 'destructive' as const,
    icon: AlertTriangle,
    color: 'text-red-600',
  },
} as const;

export function ProjectsList() {
  const workspace = useUserWorkspace();
  const accountId = workspace.user.id;
  const [projects, setProjects] = useState<ProjectData[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadProjects = async () => {
      try {
        const result = await getProjectsAction({});
        if (result.success) {
          setProjects(
            result.data.map((project) => ({
              id: project.id,
              title: project.title,
              description: project.description,
              status: project.status,
              progress: project.progress,
              created_at: project.created_at,
              segmentCount: 0, // TODO: 从segments表计算
            })),
          );
        }
      } catch (error) {
        console.error('Error loading projects:', error);
      } finally {
        setLoading(false);
      }
    };

    loadProjects();
  }, [accountId]);

  if (loading) {
    return (
      <div className="grid gap-8 pb-8 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 2xl:grid-cols-4">
        {Array.from({ length: 8 }).map((_, i) => (
          <motion.div
            key={i}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.4, delay: i * 0.1 }}
          >
            <Card className="h-80 border-0 bg-card/50 backdrop-blur-sm shadow-lg ring-1 ring-border/50">
              {/* Status bar skeleton */}
              <Skeleton className="h-1 w-full rounded-none" />

              <CardHeader className="pb-4 pt-6">
                <div className="space-y-3">
                  <Skeleton className="h-6 w-3/4" />
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-4 w-2/3" />
                </div>
              </CardHeader>

              <CardContent className="space-y-6">
                {/* Status section */}
                <div className="space-y-4">
                  <div className="flex items-center space-x-3">
                    <Skeleton className="h-8 w-8 rounded-lg" />
                    <div className="space-y-2">
                      <Skeleton className="h-5 w-20" />
                      <Skeleton className="h-3 w-16" />
                    </div>
                  </div>
                  <Skeleton className="h-2 w-full" />
                </div>

                {/* Metadata section */}
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <Skeleton className="h-4 w-24" />
                    <Skeleton className="h-4 w-20" />
                  </div>
                  <Skeleton className="h-10 w-full" />
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>
    );
  }

  if (projects.length === 0) {
    return (
      <motion.div
        className="flex h-[400px] items-center justify-center"
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5 }}
      >
        <div className="space-y-8 text-center max-w-md">
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ duration: 0.5, delay: 0.2, type: "spring" }}
            className="mx-auto flex h-32 w-32 items-center justify-center rounded-2xl bg-muted/30 ring-1 ring-border"
          >
            <VideoIcon className="h-16 w-16 text-muted-foreground/60" />
          </motion.div>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
            className="space-y-4"
          >
            <h3 className="text-2xl font-bold tracking-tight">
              <Trans i18nKey="novel2video:projects.empty.title" />
            </h3>
            <p className="text-muted-foreground leading-relaxed">
              <Trans i18nKey="novel2video:projects.empty.description" />
            </p>
          </motion.div>
        </div>
      </motion.div>
    );
  }

  return (
    <div className="grid gap-6 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
      {projects.map((project) => (
        <ProjectCard
          key={project.id}
          project={project}
        />
      ))}
    </div>
  );
}

interface ProjectCardProps {
  project: ProjectData;
}

function ProjectCard({ project }: ProjectCardProps) {
  const StatusIcon = statusConfig[project.status].icon;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      whileHover={{ y: -4, scale: 1.01 }}
      className="h-full"
    >
      <Card className="group relative h-80 overflow-hidden border-0 bg-card/50 backdrop-blur-sm shadow-lg ring-1 ring-border/50 transition-all duration-500 hover:shadow-2xl hover:ring-primary/20">
        {/* Subtle gradient overlay */}
        <div className="absolute inset-0 bg-gradient-to-br from-background/80 via-transparent to-muted/20 opacity-0 transition-opacity duration-500 group-hover:opacity-100" />

        {/* Status indicator bar */}
        <div className={`absolute top-0 left-0 right-0 h-1 bg-gradient-to-r ${project.status === 'completed' ? 'from-green-500 to-emerald-500' :
          project.status === 'processing' ? 'from-blue-500 to-cyan-500' :
            project.status === 'failed' ? 'from-red-500 to-rose-500' :
              'from-muted to-muted-foreground'
          }`} />

        <CardHeader className="relative pb-4 pt-6">
          <div className="flex items-start justify-between">
            <div className="min-w-0 flex-1 space-y-3">
              <motion.div
                whileHover={{ x: 2 }}
                transition={{ duration: 0.2 }}
              >
                <CardTitle className="line-clamp-2 text-lg font-bold leading-tight tracking-tight">
                  {project.title}
                </CardTitle>
              </motion.div>
              <If condition={Boolean(project.description)}>
                <p className="line-clamp-3 text-sm text-muted-foreground leading-relaxed">
                  {project.description}
                </p>
              </If>
            </div>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <motion.div
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                >
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0 opacity-0 transition-all duration-300 group-hover:opacity-100 hover:bg-muted/80"
                  >
                    <MoreHorizontalIcon className="h-4 w-4" />
                  </Button>
                </motion.div>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48">
                <DropdownMenuItem asChild>
                  <Link href={`/home/<USER>/projects/${project.id}`}>
                    <PlayIcon className="mr-2 h-4 w-4" />
                    <Trans i18nKey="novel2video:actions.open" />
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <FileTextIcon className="mr-2 h-4 w-4" />
                  <Trans i18nKey="novel2video:actions.duplicate" />
                </DropdownMenuItem>
                <DropdownMenuItem className="text-destructive">
                  <AlertTriangle className="mr-2 h-4 w-4" />
                  <Trans i18nKey="novel2video:actions.delete" />
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </CardHeader>

        <CardContent className="relative flex flex-1 flex-col justify-between space-y-6 pb-6">
          {/* Status and progress */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <motion.div
                whileHover={{ scale: 1.05 }}
                className="flex items-center space-x-3"
              >
                <div className={`p-2 rounded-lg ${project.status === 'completed' ? 'bg-green-100 dark:bg-green-900/20' :
                  project.status === 'processing' ? 'bg-blue-100 dark:bg-blue-900/20' :
                    project.status === 'failed' ? 'bg-red-100 dark:bg-red-900/20' :
                      'bg-muted'
                  }`}>
                  <StatusIcon className={`h-4 w-4 ${statusConfig[project.status].color}`} />
                </div>
                <div className="space-y-1">
                  <Badge
                    variant={statusConfig[project.status].variant}
                    className="text-xs font-medium px-2 py-1"
                  >
                    <Trans i18nKey={statusConfig[project.status].label} />
                  </Badge>
                  <div className="text-xs text-muted-foreground font-medium">
                    {project.progress}% complete
                  </div>
                </div>
              </motion.div>
            </div>

            <If condition={project.progress > 0}>
              <div className="space-y-2">
                <div className="relative">
                  <Progress value={project.progress} className="h-2 bg-muted/50" />
                  <motion.div
                    className="absolute inset-0 rounded-full bg-gradient-to-r from-primary/30 to-primary/10 pointer-events-none"
                    initial={{ width: 0 }}
                    animate={{ width: `${project.progress}%` }}
                    transition={{ duration: 1.5, ease: "easeOut" }}
                  />
                </div>
              </div>
            </If>
          </div>

          {/* Project metadata */}
          <div className="space-y-3">
            <div className="flex items-center justify-between text-sm">
              <motion.div
                className="flex items-center space-x-2 text-muted-foreground"
                whileHover={{ scale: 1.05, color: 'hsl(var(--foreground))' }}
                transition={{ duration: 0.2 }}
              >
                <FileTextIcon className="h-4 w-4" />
                <span className="font-medium">
                  <Trans
                    i18nKey="novel2video:projects.segmentCount"
                    values={{ count: project.segmentCount || 0 }}
                  />
                </span>
              </motion.div>
              <motion.div
                className="flex items-center space-x-2 text-muted-foreground"
                whileHover={{ scale: 1.05, color: 'hsl(var(--foreground))' }}
                transition={{ duration: 0.2 }}
              >
                <CalendarIcon className="h-4 w-4" />
                <span className="font-medium">{new Date(project.created_at).toLocaleDateString()}</span>
              </motion.div>
            </div>

            {/* Action button */}
            <Button
              asChild
              className="w-full shadow-md hover:shadow-lg transition-all duration-300 bg-primary/90 hover:bg-primary"
              size="default"
            >
              <motion.div
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <Link href={`/home/<USER>/projects/${project.id}`} className="w-full">
                  <motion.div
                    className="flex items-center justify-center space-x-2"
                    whileHover={{ x: 2 }}
                    transition={{ duration: 0.2 }}
                  >
                    <PlayIcon className="h-4 w-4" />
                    <span className="font-medium"><Trans i18nKey="novel2video:actions.open" /></span>
                  </motion.div>
                </Link>
              </motion.div>
            </Button>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}
