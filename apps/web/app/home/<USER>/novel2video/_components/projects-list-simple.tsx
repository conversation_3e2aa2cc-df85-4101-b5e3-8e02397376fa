'use client';

import { useEffect, useState } from 'react';

import {
  CalendarIcon,
  FileTextIcon,
  MoreHorizontalIcon,
  PlayIcon,
  CheckCircleIcon,
  ClockIcon,
  AlertTriangle,
} from 'lucide-react';
import Link from 'next/link';

import { Badge } from '@kit/ui/badge';
import { Button } from '@kit/ui/button';
import { Card, CardContent, CardHeader } from '@kit/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@kit/ui/dropdown-menu';
import { If } from '@kit/ui/if';
import { Progress } from '@kit/ui/progress';
import { Skeleton } from '@kit/ui/skeleton';
import { Trans } from '@kit/ui/trans';

import { getProjectsAction } from '../_lib/server/server-actions';

type ProjectStatus = 'draft' | 'processing' | 'completed' | 'failed';

interface ProjectData {
  id: string;
  title: string;
  description?: string;
  status: ProjectStatus;
  progress: number;
  segmentCount?: number;
  created_at: string;
}

const statusConfig = {
  draft: {
    label: 'novel2video:status.project.draft',
    variant: 'secondary' as const,
    icon: ClockIcon,
    color: 'text-muted-foreground',
  },
  processing: {
    label: 'novel2video:status.project.processing',
    variant: 'default' as const,
    icon: ClockIcon,
    color: 'text-blue-600',
  },
  completed: {
    label: 'novel2video:status.project.completed',
    variant: 'default' as const,
    icon: CheckCircleIcon,
    color: 'text-green-600',
  },
  failed: {
    label: 'novel2video:status.project.failed',
    variant: 'destructive' as const,
    icon: AlertTriangle,
    color: 'text-red-600',
  },
};

export function ProjectsList() {
  const [projects, setProjects] = useState<ProjectData[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadProjects = async () => {
      try {
        const result = await getProjectsAction();
        if (result.success) {
          setProjects(result.data || []);
        }
      } catch (error) {
        console.error('Failed to load projects:', error);
      } finally {
        setLoading(false);
      }
    };

    loadProjects();
  }, []);

  if (loading) {
    return (
      <div className="grid gap-6 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
        {Array.from({ length: 6 }).map((_, i) => (
          <Card key={i}>
            <CardHeader className="space-y-3">
              <Skeleton className="h-5 w-3/4" />
              <Skeleton className="h-4 w-full" />
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <Skeleton className="h-5 w-16" />
                <Skeleton className="h-4 w-10" />
              </div>
              <Skeleton className="h-2 w-full" />
              <div className="flex justify-between">
                <Skeleton className="h-4 w-20" />
                <Skeleton className="h-4 w-16" />
              </div>
              <Skeleton className="h-9 w-full" />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (projects.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-12 text-center">
        <div className="space-y-4">
          <div className="mx-auto h-12 w-12 rounded-full bg-muted flex items-center justify-center">
            <FileTextIcon className="h-6 w-6 text-muted-foreground" />
          </div>
          <div className="space-y-2">
            <h3 className="text-lg font-semibold">
              <Trans i18nKey="novel2video:dashboard.empty.title" />
            </h3>
            <p className="text-muted-foreground max-w-md">
              <Trans i18nKey="novel2video:dashboard.empty.description" />
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="grid gap-6 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
      {projects.map((project) => (
        <ProjectCard key={project.id} project={project} />
      ))}
    </div>
  );
}

interface ProjectCardProps {
  project: ProjectData;
}

function ProjectCard({ project }: ProjectCardProps) {
  const StatusIcon = statusConfig[project.status].icon;

  return (
    <Card className="group relative overflow-hidden transition-all duration-200 hover:shadow-md">
      <CardHeader className="space-y-3 pb-4">
        <div className="flex items-start justify-between">
          <div className="flex-1 space-y-2">
            <h3 className="line-clamp-2 text-lg font-semibold leading-tight">
              {project.title}
            </h3>
            <If condition={Boolean(project.description)}>
              <p className="line-clamp-2 text-sm text-muted-foreground">
                {project.description}
              </p>
            </If>
          </div>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
              >
                <MoreHorizontalIcon className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              <DropdownMenuItem className="text-destructive focus:text-destructive">
                <AlertTriangle className="mr-2 h-4 w-4" />
                <Trans i18nKey="common:delete" />
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Status and progress */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <StatusIcon className={`h-4 w-4 ${statusConfig[project.status].color}`} />
              <Badge variant={statusConfig[project.status].variant} className="text-xs">
                <Trans i18nKey={statusConfig[project.status].label} />
              </Badge>
            </div>
            <span className="text-xs text-muted-foreground font-medium">
              {project.progress}%
            </span>
          </div>

          <If condition={project.progress > 0}>
            <Progress value={project.progress} className="h-2" />
          </If>
        </div>

        {/* Project metadata */}
        <div className="flex items-center justify-between text-xs text-muted-foreground">
          <div className="flex items-center space-x-1">
            <FileTextIcon className="h-3 w-3" />
            <span>
              <Trans
                i18nKey="novel2video:projects.segmentCount"
                values={{ count: project.segmentCount || 0 }}
              />
            </span>
          </div>
          <div className="flex items-center space-x-1">
            <CalendarIcon className="h-3 w-3" />
            <span>{new Date(project.created_at).toLocaleDateString()}</span>
          </div>
        </div>

        {/* Action button */}
        <Button asChild className="w-full" size="sm">
          <Link href={`/home/<USER>/projects/${project.id}`}>
            <PlayIcon className="mr-2 h-4 w-4" />
            <Trans i18nKey="novel2video:actions.open" />
          </Link>
        </Button>
      </CardContent>
    </Card>
  );
}
