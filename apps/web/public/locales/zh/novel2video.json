{"pageTitle": "Novel2Video - AI小说转视频转换器", "title": "小说转视频", "description": "将小说文本转换为视频内容", "dashboard": {"badge": "AI智能创作", "title": "Novel2Video 工作室", "description": "使用AI驱动的文本转语音和图像生成技术，将您的小说和文章转换为引人入胜的视频。", "createProject": "创建新项目", "yourProjects": "我的项目", "projectsDescription": "管理和跟踪您的小说转视频转换项目", "empty": {"title": "暂无项目", "description": "创建您的第一个小说转视频项目，开始您的创作之旅"}}, "features": {"textProcessing": {"title": "智能文本处理", "description": "自动将文本分割为合适的片段，优化视频节奏"}, "aiGeneration": {"title": "AI内容生成", "description": "使用先进的AI模型生成自然的旁白和相关图像"}, "videoComposition": {"title": "视频合成", "description": "将音频和图像组合成精美的最终视频，支持自定义设置"}}, "status": {"all": "全部状态", "project": {"draft": "草稿", "processing": "处理中", "completed": "已完成", "failed": "失败"}, "segment": {"pending": "等待中", "processing_text": "处理文本中", "processing_audio": "生成音频中", "processing_image": "生成图像中", "completed": "已完成", "failed": "失败"}, "task": {"pending": "等待中", "running": "运行中", "completed": "已完成", "failed": "失败", "cancelled": "已取消"}}, "actions": {"open": "打开", "duplicate": "复制", "delete": "删除", "createProject": "创建项目", "preview": "预览", "settings": "设置", "export": "导出", "startProcessing": {"title": "开始处理", "success": "处理已成功启动！", "error": "启动处理失败"}, "pauseProcessing": {"title": "暂停处理", "success": "处理已成功暂停！", "error": "暂停处理失败"}, "resumeProcessing": "继续处理", "downloadVideo": "下载视频", "generatePrompt": "生成提示词", "regeneratePrompt": "重新生成提示词", "stats": {"totalSegments": "总段落", "pendingSegments": "待处理", "progress": "进度"}, "errors": {"startFailed": "启动处理失败", "startError": "启动处理失败: {{error}}", "pauseFailed": "暂停处理失败", "pauseError": "暂停处理失败: {{error}}", "noSegments": "没有待处理的段落", "taskFailed": "启动任务失败", "taskError": "启动任务失败: {{error}}"}}, "createProject": {"dialogTitle": "创建新项目", "dialogDescription": "输入项目信息和文本内容，开始创建您的视频作品", "title": "项目标题 *", "description": "项目描述", "titleDescription": "项目标题将作为生成视频的主要标识", "descriptionHint": "描述信息将帮助AI更好地理解内容风格", "content": "文本内容 *", "directInput": "直接输入", "fileUpload": "文件上传", "uploadPrompt": "点击选择文件或拖拽到这里", "contentPreview": "内容预览", "contentHint": "文本将自动分段处理，建议包含完整的情节或章节", "readyToCreate": "准备创建项目", "readyDescription": "系统将自动处理您的文本并生成精彩的视频内容", "projectTitle": "项目标题", "projectDescription": "项目描述", "wordCount": "字数", "estimatedSegments": "预计段落", "sourceFile": "源文件", "whatHappensNext": "接下来将会发生什么？", "step1": "系统将自动分析并切分文本内容", "step2": "提取文本中的人物和场景信息", "step3": "为每个段落生成合适的图像描述", "step4": "您可以在项目详情页面查看和调整生成设置", "createButton": "创建项目", "creating": "创建中...", "titlePlaceholder": "给你的项目起个好名字...", "titleLabel": "项目标题", "descriptionLabel": "项目描述", "descriptionPlaceholder": "简单描述一下这个项目...", "uploadFile": "上传文本文件", "uploadHint": "支持.txt文件，最大5MB", "textLabel": "文本内容", "textPlaceholder": "在这里输入或粘贴你的小说/文章内容...\n\n你可以：\n• 直接在此输入文本\n• 或者使用上方的文件上传功能\n• 支持纯文本格式，字数限制 10-50,000 字", "textHint": "文本长度应在 {{min}} 到 {{max}} 字符之间，当前：{{current}} 字符", "projectInfo": "项目信息", "textImport": "文本导入", "fileUploadSuccess": "文件上传成功！", "uploadProgress": "上传中... {{progress}}%", "removeFile": "移除文件", "dragDropActive": "松开鼠标即可上传", "dragDropInactive": "拖拽文件到此处或点击上传", "supportedFiles": "支持 .txt 文件，最大 5MB", "estimatedDuration": "预计生成时长: {{minutes}} 分钟", "steps": {"info": "项目信息", "content": "文本内容"}, "errors": {"titleRequired": "请输入项目标题", "titleTooLong": "项目标题不能超过255个字符", "textTooShort": "文本内容至少需要10个字符", "textTooLong": "文本内容不能超过50000个字符", "fileTooLarge": "文件大小不能超过5MB", "invalidFileType": "仅支持文本文件（.txt）", "createFailed": "创建失败，请重试"}, "success": "项目创建成功！", "create": "创建项目"}, "projects": {"title": "我的项目", "description": "管理您的小说转视频项目", "empty": {"title": "暂无项目", "description": "点击【创建项目】按钮开始您的第一个项目"}, "loading": "加载项目中...", "segmentCount": "{{count}} 个片段", "lastUpdated": "最后更新：{{date}}"}, "project": {"pageTitle": "项目详情", "pause": "暂停", "startProcessing": "开始处理", "segmentsUnit": "个片段", "resegmentText": "重新分段", "settings": "项目设置", "loadError": "项目加载失败", "notFound": {"title": "项目不存在", "description": "找不到指定的项目"}, "details": {"buttonText": "详情", "title": "项目信息", "status": "状态", "progress": "进度", "createdAt": "创建时间", "lastModified": "最后修改"}, "tabs": {"characters": "人物", "segments": "片段", "preview": "预览", "settings": "设置"}, "progress": {"title": "处理进度", "overall": "总体进度：{{progress}}%", "completed": "已完成", "processing": "处理中", "pending": "等待中", "overallProgress": "总体进度", "audioGeneration": "音频生成", "imageGeneration": "图像生成", "generated": "已生成", "progressSummary": "{{progress}}% • 已完成 {{completed}} / {{total}} 个片段", "generatePrompts": "生成提示词", "generatingPrompts": "生成中...", "generateImages": "生成图像", "generatingImages": "生成中...", "allCompleted": "全部完成", "waitingForPrompts": "等待提示词", "pendingCount": "{{count}} 个待处理", "videoGeneration": "生成视频", "generatingVideo": "生成中...", "generateAudio": "生成音频", "generatingAudio": "生成音频中...", "pendingAudioCount": "{{count}} 个待生成", "allAudioCompleted": "全部完成", "composeVideo": "合成视频", "readyToCompose": "准备合成", "composingCompleted": "合成完成", "waitingForAssets": "等待素材完成"}, "segments": {"title": "文本片段", "index": "片段 {{index}}", "segmentNumber": "片段 {{number}}", "text": "文本", "audio": "音频", "image": "图像", "count": "{{count}} 个片段", "searchPlaceholder": "搜索文本片段...", "noSegments": "暂无文本片段", "actions": {"edit": "编辑", "delete": "删除", "generatePrompt": "生成提示词", "regeneratePrompt": "重新生成提示词", "generateImage": "生成图像"}, "edit": {"title": "编辑片段 {{index}}", "textLabel": "文本内容", "contentLabel": "文本内容", "saving": "保存中...", "save": "保存", "success": "片段更新成功！", "error": "片段更新失败", "textPlaceholder": "请输入文本内容...", "errors": {"textRequired": "文本内容不能为空", "textTooLong": "文本内容不能超过1000字符"}}, "delete": {"title": "删除片段", "description": "确定要删除这个文本片段吗？此操作无法撤销。", "warning": "⚠️ 此操作无法撤销！", "confirm": "删除", "deleting": "删除中...", "success": "片段删除成功！", "error": "片段删除失败"}, "resegment": {"title": "重新切分文本", "description": "修改文本内容并重新切分。现有的片段和生成内容将被删除。", "warning": {"title": "数据丢失警告", "description": "此操作将删除所有现有片段、音频和图片，无法撤销！"}, "textLabel": "文本内容", "textPlaceholder": "请输入要分割的文本内容...", "action": "重新切分", "confirm": "重新切分文本", "processing": "处理中...", "success": "文本重新切分成功！", "error": "文本重新切分失败", "textCount": "{{count}}/{{max}} 字符", "errors": {"textRequired": "文本内容不能为空", "textTooLong": "文本内容不能超过50000字符"}}, "filterAll": "全部状态", "filterPending": "等待中", "filterProcessingAudio": "生成音频中", "filterProcessingImage": "生成图像中", "filterCompleted": "已完成", "filterFailed": "失败", "viewModes": {"list": "列表视图", "grid": "网格视图", "table": "表格视图"}, "table": {"segment": "片段", "content": "内容", "imagePrompt": "图像提示词", "status": "状态", "audio": "音频", "image": "图片"}, "pagination": {"showing": "显示第 {{start}} - {{end}} 条，共 {{total}} 个片段"}}, "preview": {"title": "视频预览", "description": "完成所有步骤后，您可以在这里预览最终视频", "generating": "视频生成中...", "noVideo": "暂无视频", "videoReady": "视频已准备就绪", "generateMessage": "视频生成中，请稍候...", "startProcessingMessage": "请先完成所有处理步骤以生成视频"}, "characters": {"create": {"title": "添加人物", "nameLabel": "人物名称", "namePlaceholder": "输入人物名称", "aliasesLabel": "别名", "aliasesPlaceholder": "多个别名用逗号分隔，如：张真人, 老道", "imagePromptLabel": "形象描述", "imagePromptPlaceholder": "描述人物的外貌特征、服装等（可选）", "creating": "创建中...", "create": "创建人物", "success": "人物创建成功", "errors": {"nameRequired": "人物名称不能为空", "nameTooLong": "人物名称过长", "createFailed": "创建失败"}}, "edit": {"title": "编辑人物", "nameLabel": "人物名称", "namePlaceholder": "输入人物名称", "aliasesLabel": "别名", "aliasesPlaceholder": "多个别名用逗号分隔，如：张真人, 老道", "imagePromptLabel": "形象描述", "imagePromptPlaceholder": "描述人物的外貌特征、服装等（可选）", "cancel": "取消", "saving": "保存中...", "save": "保存更改", "success": "人物更新成功", "errors": {"nameRequired": "人物名称不能为空", "nameTooLong": "人物名称过长", "updateFailed": "更新失败"}}}}, "validation": {"titleRequired": "项目标题不能为空", "titleTooLong": "项目标题不能超过255个字符", "textRequired": "文本内容不能为空", "textMinLength": "文本内容至少需要 {{min}} 个字符", "textMaxLength": "文本内容不能超过 {{max}} 个字符"}, "projectSettings": {"title": "项目设置", "subtitle": "配置音频、图像和视频生成参数", "loading": "加载设置中...", "saveSuccess": "设置保存成功！", "saveError": "设置保存失败", "loadError": "设置加载失败，使用默认设置", "resetSuccess": "已重置为默认设置", "cancel": "取消", "save": "保存", "reset": "重置为默认", "progress": "{{count}}/{{total}} 项", "completed": "已完成", "configure": "配置", "audio": {"title": "音频设置", "description": "配置语音合成和背景音乐", "basicConfig": "基础配置", "advancedOptions": "高级选项", "workflowConfig": "工作流配置", "workflowConfigPlaceholder": "选择音频生成工作流", "workflowConfigLoading": "加载配置中...", "workflowConfigDescription": "选择用于生成音频的TTS工作流配置。不同配置会影响音频生成的音质和风格。", "voice": {"label": "声音类型", "placeholder": "选择声音类型", "female1": "女声1 - 温柔", "female2": "女声2 - 活泼", "male1": "男声1 - 磁性", "male2": "男声2 - 稳重"}, "speed": {"label": "语速：{{speed}}x", "slow": "慢", "fast": "快"}, "pitch": {"label": "音调", "placeholder": "选择音调", "low": "低", "normal": "正常", "high": "高"}, "emotionalTone": {"label": "情感语调", "description": "根据文本内容自动调整语调"}, "backgroundMusic": {"label": "背景音乐", "description": "为视频添加背景音乐"}, "musicVolume": {"label": "背景音乐音量：{{volume}}%"}}, "image": {"title": "图像设置", "description": "配置图像生成风格和质量", "styleConfig": "风格配置", "effectConfig": "效果配置", "style": {"label": "艺术风格", "placeholder": "选择艺术风格", "realistic": "写实", "artistic": "艺术", "anime": "动漫", "cartoon": "卡通", "cinematic": "电影级", "watercolor": "水彩"}, "aspectRatio": {"label": "长宽比", "placeholder": "选择长宽比"}, "quality": {"label": "图像质量", "placeholder": "选择图像质量", "standard": "标准", "high": "高", "ultra": "超高"}, "colorPalette": {"label": "色彩调色板", "placeholder": "选择色彩调色板", "vibrant": "鲜艳", "pastel": "淡雅", "monochrome": "单色", "warm": "暖色调", "cool": "冷色调"}, "lightingStyle": {"label": "光影风格", "placeholder": "选择光影风格", "natural": "自然", "dramatic": "戏剧性", "soft": "柔和", "goldenHour": "黄金时刻"}, "faceEnhancement": {"label": "面部增强", "description": "自动增强面部细节"}}, "video": {"title": "视频设置", "description": "配置最终视频输出参数", "outputConfig": "输出配置", "effectOptions": "效果选项", "resolution": {"label": "分辨率", "placeholder": "选择分辨率"}, "frameRate": {"label": "帧率", "placeholder": "选择帧率"}, "format": {"label": "输出格式", "placeholder": "选择输出格式"}, "transitions": {"label": "过场动画", "description": "在片段之间添加过渡动画"}, "transitionStyle": {"label": "过渡风格", "placeholder": "选择过渡风格", "fade": "淡化", "slide": "滑动", "zoom": "缩放", "dissolve": "溶解"}, "subtitles": {"label": "字幕", "description": "显示文本字幕"}, "subtitleStyle": {"label": "字幕风格", "placeholder": "选择字幕风格", "modern": "现代", "classic": "经典", "minimal": "简约"}}}, "filters": {"allStatus": "全部状态"}, "segment": {"imagePrompt": "图像提示词", "expand": "展开", "collapse": "收起", "promptNotGenerated": "未生成"}, "generateImage": {"title": "生成图像 - 片段 {{index}}", "description": "使用当前片段的图像提示词生成图像", "imagePromptLabel": "图像提示词", "workflowConfigLabel": "工作流配置", "workflowConfigPlaceholder": "选择工作流配置", "loadingConfigs": "加载中...", "widthLabel": "宽度：{{width}}px", "heightLabel": "高度：{{height}}px", "quickSizes": "快速尺寸", "square": "正方形", "landscape169": "横屏 16:9", "portrait916": "竖屏 9:16", "landscape43": "横屏 4:3", "portrait34": "竖屏 3:4", "cancel": "取消", "generate": "生成图像", "generating": "创建任务中...", "success": "图像生成任务创建成功！图像将很快可用。", "error": "创建图像生成任务失败", "recommended": "推荐"}, "segments": {"actions": {"edit": "编辑", "delete": "删除", "generatePrompt": "生成提示词", "regeneratePrompt": "重新生成提示词", "generateImage": "生成图片", "regenerateImage": "重新生成图片", "generateAudio": "生成音频", "playAudio": "播放音频", "pauseAudio": "暂停音频", "analyzeCharacters": "分析人物", "reanalyzeCharacters": "重新分析人物"}, "details": {"characters": "出场人物", "imagePrompt": "图像提示词", "audioPreview": "音频预览"}, "status": {"updated": "已更新"}, "empty": {"title": "没有找到段落", "description": "当前筛选条件下没有段落。请尝试调整搜索条件或状态筛选。"}}, "uploadFile": "上传文件", "generatePrompts": "生成提示词", "generateImages": "生成图像", "generateAudio": "生成音频", "mergeVideo": "合并视频", "projectCreated": "项目创建成功", "fileUploaded": "文件上传成功", "fileUploadFailed": "文件上传失败", "uploadNovelFile": "上传小说文件", "selectFile": "选择文件", "supportedFormats": "支持格式：TXT、PDF、DOCX", "maxFileSize": "最大文件大小：10MB", "noProjectsFound": "未找到项目", "createFirstProject": "创建您的第一个项目开始使用", "projectDetails": "项目详情", "createdAt": "创建时间", "updatedAt": "更新时间", "segmentCount": "片段数量", "processing": "处理中", "completed": "已完成", "failed": "失败", "pending": "等待中", "retrying": "重试中", "imagePrompt": "图像提示词", "noImagePrompt": "尚未生成图像提示词", "promptGenerationFailed": "提示词生成失败", "promptGenerationInProgress": "提示词生成中", "batchProcessing": "批量处理中", "promptErrors": "部分提示词生成失败", "retryableErrors": "发生临时错误 - 将重试", "permanentErrors": "部分片段永久失败", "errorDetails": "错误详情", "taskMonitor": {"title": "任务监控", "description": "监控和管理正在进行的任务", "loadError": "加载任务列表失败", "noTasks": "暂无任务", "cancelSuccess": "任务已取消", "cancelError": "取消任务失败: {{error}}", "retrySuccess": "任务已重试", "retryError": "重试任务失败: {{error}}", "createdAt": "创建时间: {{date}}", "actions": {"retry": "重试", "cancel": "取消", "view": "查看详情"}}, "taskType": {"generate_prompts": "生成提示词", "generate_image": "生成图片", "generate_audio": "生成音频", "generate_video": "生成视频"}, "prompts": {"styles": {"realistic": "写实场景特写，", "artistic": "艺术插画，戏剧性光影，", "anime": "动漫风格场景特写，", "cartoon": "生动的卡通场景，", "cinematic": "电影级特写镜头，", "watercolor": "水彩画风格，"}, "features": {"verticalComposition": "竖向构图", "mobileOptimized": "移动端优化", "highContrast": "高对比度", "clearFocus": "清晰焦点", "dramaticLighting": "戏剧性光影"}, "emotions": {"sad": "，悲伤情绪表现", "happy": "，欢乐愉悦表情", "angry": "，愤怒激烈表情"}, "quality": "4K画质"}, "projectActions": {"errors": {"startFailed": "启动处理失败", "startError": "启动处理失败: {{error}}", "pauseFailed": "暂停处理失败", "pauseError": "暂停处理失败: {{error}}", "noSegments": "没有待处理的片段", "taskFailed": "启动任务失败", "taskError": "启动任务失败: {{error}}"}}, "updateSegment": "更新片段", "errors": {"generatePromptsFailed": "生成提示词失败", "generateImagesFailed": "生成图片失败", "generateAudioFailed": "生成音频失败", "regenerateAudioFailed": "重新生成音频失败", "extractCharactersFailed": "人物提取失败", "analyzeCharactersFailed": "段落人物分析失败", "reanalyzeCharactersFailed": "重新分析段落人物失败", "regeneratePromptsFailed": "重新生成提示词失败", "regenerateImagesFailed": "重新生成图片失败", "videoComposeFailed": "视频合成失败", "videoRegenerateFailed": "视频重新生成失败", "videoDeleteFailed": "删除视频失败", "checkVideoReadinessFailed": "检查视频准备状态失败", "createTaskFailed": "创建任务失败", "analyzeSegmentCharactersFailed": "分析段落人物失败", "regenerateSegmentPromptFailed": "重新生成提示词失败", "generateSegmentImageFailed": "生成图片失败", "regenerateSegmentImageFailed": "重新生成图片失败", "allAudioGenerated": "所有段落都已生成音频", "needExtractCharactersFirst": "请先提取项目人物，然后再分析段落人物", "missingMediaFiles": "还有 {{count}} 个段落缺少音频或图片文件，请先完成所有媒体生成"}, "confirmDialogs": {"regenerateAllAudio": {"title": "重新生成所有音频", "message": "重新生成将覆盖已有的音频文件（当前 {{count}} 个段落已生成），是否继续？"}, "reextractCharacters": {"title": "重新提取人物", "message": "当前项目已有 {{count}} 个人物。重新提取将替换现有的人物数据，是否继续？"}, "reanalyzeAllCharacters": {"title": "重新分析段落人物", "message": "重新分析将覆盖已有的段落人物关联数据（当前 {{count}} 个段落已分析），是否继续？"}, "regenerateAllPrompts": {"title": "重新生成所有提示词", "message": "重新生成将覆盖已有的图像提示词（当前 {{count}} 个段落已生成），是否继续？"}, "regenerateAllImages": {"title": "重新生成所有图片", "message": "重新生成将覆盖已有的图片（当前 {{count}} 个段落已生成），是否继续？"}, "recomposeVideo": {"title": "重新合成视频", "message": "项目已经有视频文件，重新合成将覆盖现有视频，是否继续？"}, "regenerateVideo": {"title": "重新生成视频", "message": "确定要重新生成视频吗？这将删除现有视频并重新合成。"}, "deleteVideo": {"title": "删除视频", "message": "确定要删除这个视频吗？删除后无法恢复，需要重新生成。"}}, "successMessages": {"taskCreated": "任务已创建", "characterExtracted": "人物提取任务已创建", "charactersAnalyzed": "段落人物分析任务已创建", "charactersReanalyzed": "重新分析所有段落人物任务已创建", "promptsRegenerated": "重新生成所有提示词任务已创建", "imagesRegenerated": "重新生成所有图片任务已创建", "promptGenerated": "提示词生成任务已创建", "promptRegenerated": "提示词重新生成任务已创建", "imageGenerated": "图片生成任务已创建", "imageRegenerated": "图片重新生成任务已创建", "newImageGenerated": "新图片已生成！", "audioGenerated": "音频已生成！", "newCharacterExtracted": "新人物已提取！", "videoDeleted": "视频已删除"}, "styles": {"realistic": "写实风格"}, "contexts": {"novelScene": "小说场景描述"}, "feedback": {"checkVideoReadinessFailed": "检查视频准备状态失败", "createAnalysisTaskFailed": "创建分析任务失败", "analyzeSegmentCharactersFailed": "分析段落人物失败", "createPromptTaskFailed": "创建提示词生成任务失败", "createImageTaskFailed": "创建图片生成任务失败", "createRegeneratePromptTaskFailed": "创建提示词重新生成任务失败", "createRegenerateImageTaskFailed": "创建图片重新生成任务失败", "audioFeatureInDevelopment": "音频生成功能正在开发中...", "videoRegeneratedFailed": "视频重新生成失败", "deleteVideoFailed": "删除视频失败"}}