{"pageTitle": "Novel2Video - AI Story to Video Converter", "title": "Novel2Video", "description": "Convert novels to videos with AI", "dashboard": {"badge": "AI-Powered Creation", "title": "Novel2Video Studio", "description": "Transform your novels and articles into engaging videos with AI-powered text-to-speech and image generation.", "createProject": "Create New Project", "yourProjects": "Your Projects", "projectsDescription": "Manage and track your novel-to-video conversion projects", "empty": {"title": "No projects yet", "description": "Create your first Novel2Video project to get started transforming text into engaging videos."}}, "features": {"textProcessing": {"title": "Smart Text Processing", "description": "Automatically segment your text into meaningful chunks for optimal video pacing."}, "aiGeneration": {"title": "AI Content Generation", "description": "Generate natural-sounding narration and contextual images using advanced AI models."}, "videoComposition": {"title": "Video Composition", "description": "Combine audio and images into a polished final video with customizable settings."}}, "status": {"all": "All Status", "project": {"draft": "Draft", "processing": "Processing", "completed": "Completed", "failed": "Failed"}, "segment": {"pending": "Pending", "processing_text": "Processing Text", "processing_audio": "Generating Audio", "processing_image": "Generating Image", "completed": "Completed", "failed": "Failed"}, "task": {"pending": "Pending", "running": "Running", "completed": "Completed", "failed": "Failed", "cancelled": "Cancelled"}}, "actions": {"open": "Open", "duplicate": "Duplicate", "delete": "Delete", "createProject": "Create Project", "preview": "Preview", "settings": "Settings", "export": "Export", "startProcessing": {"title": "Start Processing", "success": "Processing started successfully!", "error": "Failed to start processing"}, "pauseProcessing": {"title": "Pause Processing", "success": "Processing paused successfully!", "error": "Failed to pause processing"}, "resumeProcessing": "Resume Processing", "downloadVideo": "Download Video", "generatePrompt": "Generate Prompt", "regeneratePrompt": "Regenerate Prompt", "stats": {"totalSegments": "Total Segments", "pendingSegments": "Pending", "progress": "Progress"}, "errors": {"startFailed": "Failed to start processing", "startError": "Failed to start processing: {{error}}", "pauseFailed": "Failed to pause processing", "pauseError": "Failed to pause processing: {{error}}", "noSegments": "No segments to process", "taskFailed": "Failed to start task", "taskError": "Failed to start task: {{error}}"}}, "createProject": {"dialogTitle": "Create New Project", "dialogDescription": "Enter project information and text content to start creating your video", "title": "Project Title *", "description": "Project Description", "titleDescription": "The project title will be the main identifier for your generated video", "descriptionHint": "The description helps AI better understand the content style", "content": "Text Content *", "directInput": "Direct Input", "fileUpload": "File Upload", "uploadPrompt": "Click to select file or drag and drop here", "contentPreview": "Content Preview", "contentHint": "Text will be automatically segmented, best to include complete plots or chapters", "readyToCreate": "Ready to Create Project", "readyDescription": "The system will automatically process your text and generate amazing video content", "projectTitle": "Project Title", "projectDescription": "Project Description", "wordCount": "Word Count", "estimatedSegments": "Estimated Segments", "sourceFile": "Source File", "whatHappensNext": "What happens next?", "step1": "System will automatically analyze and segment text content", "step2": "Extract characters and scene information from text", "step3": "Generate appropriate image descriptions for each segment", "step4": "You can view and adjust generation settings on the project details page", "createButton": "Create Project", "creating": "Creating...", "titlePlaceholder": "Give your project a great name...", "titleLabel": "Project Title", "descriptionLabel": "Project Description", "descriptionPlaceholder": "Briefly describe this project...", "uploadFile": "Upload Text File", "uploadHint": "Supports .txt files, max 5MB", "textLabel": "Text Content", "textPlaceholder": "Enter or paste your novel/article content here...\n\nYou can:\n• Type directly in this text area\n• Or use the file upload feature above\n• Supports plain text format, 10-50,000 characters limit", "textHint": "Text length should be between {{min}} and {{max}} characters. Current: {{current}}", "projectInfo": "Project Information", "textImport": "Text Import", "fileUploadSuccess": "File uploaded successfully!", "uploadProgress": "Uploading... {{progress}}%", "removeFile": "Remove File", "dragDropActive": "Release to upload", "dragDropInactive": "Drag file here or click to upload", "supportedFiles": "Supports .txt files, max 5MB", "estimatedDuration": "Estimated duration: {{minutes}} minutes", "steps": {"info": "Project Info", "content": "Text Content"}, "errors": {"titleRequired": "Please enter a project title", "titleTooLong": "Project title cannot exceed 255 characters", "textTooShort": "Text content must be at least 10 characters", "textTooLong": "Text content cannot exceed 50000 characters", "fileTooLarge": "File size cannot exceed 5MB", "invalidFileType": "Only text files (.txt) are supported", "createFailed": "Failed to create project, please try again"}, "success": "Project created successfully!", "create": "Create Project"}, "projects": {"title": "My Projects", "description": "Manage your novel-to-video projects", "empty": {"title": "No projects yet", "description": "Click the 'Create Project' button to start your first project"}, "loading": "Loading projects...", "segmentCount": "{{count}} segments", "lastUpdated": "Last updated: {{date}}"}, "project": {"pageTitle": "Project Details", "pause": "Pause", "startProcessing": "Start Processing", "segmentsUnit": "segments", "resegmentText": "Resegment Text", "settings": "Project Settings", "loadError": "Failed to load project", "notFound": {"title": "Project Not Found", "description": "The specified project could not be found"}, "details": {"buttonText": "Details", "title": "Project Information", "status": "Status", "progress": "Progress", "createdAt": "Created", "lastModified": "Last Modified"}, "progress": {"title": "Processing Progress", "overall": "Overall Progress: {{progress}}%", "completed": "Completed", "processing": "Processing", "pending": "Pending", "overallProgress": "Overall Progress", "audioGeneration": "Audio Generation", "imageGeneration": "Image Generation", "generated": "Generated", "progressSummary": "{{progress}}% • Completed {{completed}} / {{total}} segments", "generatePrompts": "Generate Prompts", "generatingPrompts": "Generating...", "generateImages": "Generate Images", "generatingImages": "Generating...", "allCompleted": "All Completed", "waitingForPrompts": "Waiting for Prompts", "pendingCount": "{{count}} Pending", "videoGeneration": "Generate Video", "generatingVideo": "Generating...", "generateAudio": "Generate Audio", "generatingAudio": "Generating Audio...", "pendingAudioCount": "{{count}} pending", "allAudioCompleted": "All Completed", "composeVideo": "Compose Video", "readyToCompose": "Ready to Compose", "composingCompleted": "Composition Completed", "waitingForAssets": "Waiting for Assets"}, "segments": {"title": "Text Segments", "index": "Segment {{index}}", "segmentNumber": "Segment {{number}}", "text": "Text", "audio": "Audio", "image": "Image", "count": "{{count}} segments", "searchPlaceholder": "Search text segments...", "noSegments": "No text segments", "actions": {"edit": "Edit", "delete": "Delete", "generatePrompt": "Generate Prompt", "regeneratePrompt": "Regenerate Prompt", "generateImage": "Generate Image", "regenerateImage": "Regenerate Image", "generateAudio": "Generate Audio", "playAudio": "Play Audio", "pauseAudio": "Pause Audio", "analyzeCharacters": "Analyze Characters", "reanalyzeCharacters": "Reanalyze Characters"}, "details": {"characters": "Characters", "imagePrompt": "Image Prompt", "audioPreview": "Audio Preview"}, "status": {"updated": "Updated"}, "empty": {"title": "No segments found", "description": "No segments found under current filter conditions. Try adjusting search criteria or status filters."}, "edit": {"title": "Edit Segment {{index}}", "textLabel": "Text Content", "contentLabel": "Text Content", "saving": "Saving...", "save": "Save", "success": "Segment updated successfully!", "error": "Failed to update segment", "textPlaceholder": "Please enter text content...", "errors": {"textRequired": "Text content cannot be empty", "textTooLong": "Text content cannot exceed 1000 characters"}}, "delete": {"title": "Delete Segment", "description": "Are you sure you want to delete this text segment? This action cannot be undone.", "warning": "⚠️ This action cannot be undone!", "confirm": "Delete", "deleting": "Deleting...", "success": "Segment deleted successfully!", "error": "Failed to delete segment"}, "resegment": {"title": "Resegment Text", "description": "Modify text content and resegment. Existing segments and generated content will be deleted.", "warning": {"title": "Data Loss Warning", "description": "This action will delete all existing segments, audio, and images and cannot be undone!"}, "textLabel": "Text Content", "textPlaceholder": "Please enter the text content to be segmented...", "action": "Resegment", "confirm": "Resegment Text", "processing": "Processing...", "success": "Text resegmented successfully!", "error": "Failed to resegment text", "textCount": "{{count}}/{{max}} characters", "errors": {"textRequired": "Text content cannot be empty", "textTooLong": "Text content cannot exceed 50000 characters"}}, "filterAll": "All Status", "filterPending": "Pending", "filterProcessingAudio": "Generating Audio", "filterProcessingImage": "Generating Image", "filterCompleted": "Completed", "filterFailed": "Failed", "viewModes": {"list": "List View", "grid": "Grid View", "table": "Table View"}, "table": {"segment": "Segment", "content": "Content", "imagePrompt": "Image Prompt", "status": "Status", "audio": "Audio", "image": "Image"}, "pagination": {"showing": "Showing {{start}} - {{end}} of {{total}} segments"}}, "tabs": {"characters": "Characters", "segments": "Text Segments", "settings": "Project Settings", "preview": "Preview and Export"}, "characters": {"create": {"title": "Add Character", "nameLabel": "Character Name", "namePlaceholder": "Enter character name", "aliasesLabel": "Aliases", "aliasesPlaceholder": "Multiple aliases separated by commas, e.g.: <PERSON>, Old Taoist", "imagePromptLabel": "Appearance Description", "imagePromptPlaceholder": "Describe the character's appearance, clothing, etc. (optional)", "creating": "Creating...", "create": "Create Character", "success": "Character created successfully", "errors": {"nameRequired": "Character name cannot be empty", "nameTooLong": "Character name is too long", "createFailed": "Creation failed"}}, "edit": {"title": "Edit Character", "nameLabel": "Character Name", "namePlaceholder": "Enter character name", "aliasesLabel": "Aliases", "aliasesPlaceholder": "Multiple aliases separated by commas, e.g.: <PERSON>, Old Taoist", "imagePromptLabel": "Appearance Description", "imagePromptPlaceholder": "Describe the character's appearance, clothing, etc. (optional)", "cancel": "Cancel", "saving": "Saving...", "save": "Save Changes", "success": "Character updated successfully", "errors": {"nameRequired": "Character name cannot be empty", "nameTooLong": "Character name is too long", "updateFailed": "Update failed"}}}, "projectSettings": {"title": "Project Settings"}, "preview": {"title": "Preview", "description": "Preview and download the final video once the project is completed", "generating": "Generating video...", "noVideo": "Video not ready yet", "videoReady": "Video is ready", "generateMessage": "Video is being generated, please wait...", "startProcessingMessage": "Please start processing to generate video"}, "statusTexts": {"completed": "Completed", "processingAudio": "Generating Audio", "processingImage": "Generating Image", "failed": "Failed", "pending": "Pending"}, "createdAt": "Created: {{date}}", "pagination": {"showing": "Showing {{start}} - {{end}} of {{total}} segments"}, "errors": {"noSegmentsToProcess": "No segments to process", "generatePromptsFailed": "Failed to generate prompts", "noSegmentsWithPrompts": "No segments to process (need to generate prompts first)", "noAvailableConfig": "No available image generation config", "configFetchFailed": "Failed to fetch config", "generateImagesFailed": "Failed to generate images"}}, "settings": {"audio": {"title": "Audio Settings", "voice": "Voice Type", "currentVoice": "Current: Female Voice 1", "speed": "Speed", "currentSpeed": "Current: Normal"}, "image": {"title": "Image Settings", "style": "Image Style", "currentStyle": "Current: Realistic Style", "size": "Image Dimensions", "currentSize": "Current: 16:9"}, "video": {"title": "Video Settings", "resolution": "Resolution", "currentResolution": "Current: 1080p", "frameRate": "Frame Rate", "currentFrameRate": "Current: 30fps"}}, "validation": {"titleRequired": "Project title cannot be empty", "titleTooLong": "Project title cannot exceed 255 characters", "textRequired": "Text content cannot be empty", "textMinLength": "Text content needs at least {{min}} characters", "textMaxLength": "Text content cannot exceed {{max}} characters"}, "projectSettings": {"title": "Project Settings", "subtitle": "Configure audio, image and video generation parameters", "loading": "Loading settings...", "saveSuccess": "Setting<PERSON> saved successfully!", "saveError": "Failed to save settings", "loadError": "Failed to load settings, using defaults", "resetSuccess": "Reset to default settings", "cancel": "Cancel", "save": "Save", "reset": "Reset to De<PERSON>ult", "progress": "{{count}}/{{total}} items", "completed": "Completed", "configure": "Configure", "audio": {"title": "Audio Settings", "description": "Configure voice synthesis and background music", "basicConfig": "Basic Configuration", "advancedOptions": "Advanced Options", "workflowConfig": "Workflow Configuration", "workflowConfigPlaceholder": "Select audio generation workflow", "workflowConfigLoading": "Loading configurations...", "workflowConfigDescription": "Select the TTS workflow configuration for audio generation. Different configurations affect audio quality and style.", "voice": {"label": "Voice Type", "placeholder": "Select voice type", "female1": "Female 1 - Gentle", "female2": "Female 2 - Lively", "male1": "Male 1 - Magnetic", "male2": "Male 2 - <PERSON><PERSON><PERSON>"}, "speed": {"label": "Speed: {{speed}}x", "slow": "Slow", "fast": "Fast"}, "pitch": {"label": "Pitch", "placeholder": "Select pitch", "low": "Low", "normal": "Normal", "high": "High"}, "emotionalTone": {"label": "Emotional Tone", "description": "Automatically adjust tone based on text content"}, "backgroundMusic": {"label": "Background Music", "description": "Add background music to video"}, "musicVolume": {"label": "Background Music Volume: {{volume}}%"}}, "image": {"title": "Image Settings", "description": "Configure image generation style and quality", "styleConfig": "Style Configuration", "effectConfig": "Effect Configuration", "style": {"label": "Art Style", "placeholder": "Select art style", "realistic": "Realistic", "artistic": "Artistic", "anime": "Anime", "cartoon": "Cartoon", "cinematic": "Cinematic", "watercolor": "Watercolor"}, "aspectRatio": {"label": "Aspect Ratio", "placeholder": "Select aspect ratio"}, "quality": {"label": "Image Quality", "placeholder": "Select image quality", "standard": "Standard", "high": "High", "ultra": "Ultra"}, "colorPalette": {"label": "Color Palette", "placeholder": "Select color palette", "vibrant": "Vibrant", "pastel": "Pastel", "monochrome": "Monochrome", "warm": "Warm", "cool": "Cool"}, "lightingStyle": {"label": "Lighting Style", "placeholder": "Select lighting style", "natural": "Natural", "dramatic": "Dramatic", "soft": "Soft", "goldenHour": "Golden Hour"}, "faceEnhancement": {"label": "Face Enhancement", "description": "Automatically enhance facial details"}}, "video": {"title": "Video Settings", "description": "Configure final video output parameters", "outputConfig": "Output Configuration", "effectOptions": "Effect Options", "resolution": {"label": "Resolution", "placeholder": "Select resolution"}, "frameRate": {"label": "Frame Rate", "placeholder": "Select frame rate"}, "format": {"label": "Output Format", "placeholder": "Select output format"}, "transitions": {"label": "Transitions", "description": "Add transition animations between segments"}, "transitionStyle": {"label": "Transition Style", "placeholder": "Select transition style", "fade": "Fade", "slide": "Slide", "zoom": "Zoom", "dissolve": "Dissolve"}, "subtitles": {"label": "Subtitles", "description": "Display text subtitles"}, "subtitleStyle": {"label": "Subtitle Style", "placeholder": "Select subtitle style", "modern": "Modern", "classic": "Classic", "minimal": "Minimal"}}}, "filters": {"allStatus": "All Status"}, "segment": {"imagePrompt": "Image Prompt", "expand": "Expand", "collapse": "Collapse", "promptNotGenerated": "Not Generated"}, "generateImage": {"title": "Generate Image - Segment {{index}}", "description": "Generate image using the current segment's image prompt", "imagePromptLabel": "Image Prompt", "workflowConfigLabel": "Workflow Configuration", "workflowConfigPlaceholder": "Select workflow configuration", "loadingConfigs": "Loading...", "widthLabel": "Width: {{width}}px", "heightLabel": "Height: {{height}}px", "quickSizes": "Quick Sizes", "square": "Square", "landscape169": "Landscape 16:9", "portrait916": "Portrait 9:16", "landscape43": "Landscape 4:3", "portrait34": "Portrait 3:4", "cancel": "Cancel", "generate": "Generate Image", "generating": "Creating task...", "success": "Image generation task created successfully! The image will be available shortly.", "error": "Failed to create image generation task", "recommended": "Recommended"}, "segments": "Segments", "uploadFile": "Upload File", "generatePrompts": "Generate Prompts", "generateImages": "Generate Images", "generateAudio": "Generate Audio", "mergeVideo": "Merge Video", "projectCreated": "Project created successfully", "fileUploaded": "File uploaded successfully", "fileUploadFailed": "Failed to upload file", "uploadNovelFile": "Upload Novel File", "selectFile": "Select File", "supportedFormats": "Supported formats: TXT, PDF, DOCX", "maxFileSize": "Max file size: 10MB", "noProjectsFound": "No projects found", "createFirstProject": "Create your first project to get started", "projectDetails": "Project Details", "createdAt": "Created At", "updatedAt": "Updated At", "segmentCount": "Segment Count", "processing": "Processing", "completed": "Completed", "failed": "Failed", "pending": "Pending", "retrying": "Retrying", "imagePrompt": "Image Prompt", "noImagePrompt": "No image prompt generated yet", "promptGenerationFailed": "Prompt Generation Failed", "promptGenerationInProgress": "Prompt Generation In Progress", "batchProcessing": "Processing in batches", "promptErrors": "Some prompts failed to generate", "retryableErrors": "Temporary errors occurred - will retry", "permanentErrors": "Some segments failed permanently", "errorDetails": "<PERSON><PERSON><PERSON>", "taskMonitor": {"title": "Task Monitor", "description": "Monitor and manage ongoing tasks", "loadError": "Failed to load task list", "noTasks": "No tasks", "cancelSuccess": "Task cancelled", "cancelError": "Failed to cancel task: {{error}}", "retrySuccess": "Task retried", "retryError": "Failed to retry task: {{error}}", "createdAt": "Created at: {{date}}", "actions": {"retry": "Retry", "cancel": "Cancel", "view": "View Details"}}, "taskType": {"generate_prompts": "Generate Prompts", "generate_image": "Generate Image", "generate_audio": "Generate Audio", "generate_video": "Generate Video"}, "prompts": {"styles": {"realistic": "Close-up realistic scene,", "artistic": "Artistic illustration with dramatic lighting,", "anime": "Anime-style close-up scene,", "cartoon": "Vibrant cartoon scene,", "cinematic": "Cinematic close-up shot,", "watercolor": "Watercolor painting style,"}, "features": {"verticalComposition": "vertical composition", "mobileOptimized": "mobile optimized", "highContrast": "high contrast", "clearFocus": "clear focus", "dramaticLighting": "dramatic lighting"}, "emotions": {"sad": ", sad emotional expression", "happy": ", happy joyful expression", "angry": ", angry fierce expression"}, "quality": "4K quality"}, "projectActions": {"errors": {"startFailed": "Failed to start processing", "startError": "Failed to start processing: {{error}}", "pauseFailed": "Failed to pause processing", "pauseError": "Failed to pause processing: {{error}}", "noSegments": "No segments to process", "taskFailed": "Failed to start task", "taskError": "Failed to start task: {{error}}"}}, "updateSegment": "Update Segment", "errors": {"generatePromptsFailed": "Failed to generate prompts", "generateImagesFailed": "Failed to generate images", "generateAudioFailed": "Failed to generate audio", "regenerateAudioFailed": "Failed to regenerate audio", "extractCharactersFailed": "Failed to extract characters", "analyzeCharactersFailed": "Failed to analyze segment characters", "reanalyzeCharactersFailed": "Failed to reanalyze segment characters", "regeneratePromptsFailed": "Failed to regenerate prompts", "regenerateImagesFailed": "Failed to regenerate images", "videoComposeFailed": "Failed to compose video", "videoRegenerateFailed": "Failed to regenerate video", "videoDeleteFailed": "Failed to delete video", "checkVideoReadinessFailed": "Failed to check video readiness", "createTaskFailed": "Failed to create task", "analyzeSegmentCharactersFailed": "Failed to analyze segment characters", "regenerateSegmentPromptFailed": "Failed to regenerate prompt", "generateSegmentImageFailed": "Failed to generate image", "regenerateSegmentImageFailed": "Failed to regenerate image", "allAudioGenerated": "All segments already have audio", "needExtractCharactersFirst": "Please extract project characters first before analyzing segment characters", "missingMediaFiles": "{{count}} segments are missing audio or image files, please complete all media generation first"}, "confirmDialogs": {"regenerateAllAudio": {"title": "Regenerate All Audio", "message": "Regenerating will overwrite existing audio files ({{count}} segments currently generated), continue?"}, "reextractCharacters": {"title": "Re-extract Characters", "message": "Project currently has {{count}} characters. Re-extracting will replace existing character data, continue?"}, "reanalyzeAllCharacters": {"title": "Reanalyze Segment Characters", "message": "Reanalyzing will overwrite existing segment-character associations ({{count}} segments currently analyzed), continue?"}, "regenerateAllPrompts": {"title": "Regenerate All Prompts", "message": "Regenerating will overwrite existing image prompts ({{count}} segments currently generated), continue?"}, "regenerateAllImages": {"title": "Regenerate All Images", "message": "Regenerating will overwrite existing images ({{count}} segments currently generated), continue?"}, "recomposeVideo": {"title": "Recompose Video", "message": "Project already has a video file, recomposing will overwrite the existing video, continue?"}, "regenerateVideo": {"title": "Regenerate Video", "message": "Are you sure you want to regenerate the video? This will delete the existing video and recompose."}, "deleteVideo": {"title": "Delete Video", "message": "Are you sure you want to delete this video? It cannot be recovered and will need to be regenerated."}}, "successMessages": {"taskCreated": "Task created", "characterExtracted": "Character extraction task created", "charactersAnalyzed": "Segment character analysis task created", "charactersReanalyzed": "Reanalyze all segment characters task created", "promptsRegenerated": "Regenerate all prompts task created", "imagesRegenerated": "Regenerate all images task created", "promptGenerated": "Prompt generation task created", "promptRegenerated": "Prompt regeneration task created", "imageGenerated": "Image generation task created", "imageRegenerated": "Image regeneration task created", "newImageGenerated": "New image generated!", "audioGenerated": "Audio generated!", "newCharacterExtracted": "New character extracted!", "videoDeleted": "Video deleted"}, "styles": {"realistic": "Realistic Style"}, "contexts": {"novelScene": "Novel Scene Description"}, "feedback": {"checkVideoReadinessFailed": "Failed to check video readiness", "createAnalysisTaskFailed": "Failed to create analysis task", "analyzeSegmentCharactersFailed": "Failed to analyze segment characters", "createPromptTaskFailed": "Failed to create prompt generation task", "createImageTaskFailed": "Failed to create image generation task", "createRegeneratePromptTaskFailed": "Failed to create prompt regeneration task", "createRegenerateImageTaskFailed": "Failed to create image regeneration task", "audioFeatureInDevelopment": "Audio generation feature is under development...", "videoRegeneratedFailed": "Failed to regenerate video", "deleteVideoFailed": "Failed to delete video"}}